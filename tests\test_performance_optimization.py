#!/usr/bin/env python3
"""
性能优化功能单元测试
测试RTK查询优化和评分缓存系统
"""

import unittest
import numpy as np
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.simple_distance_matcher import SimpleDistanceMatcher, ScoreCache, PerformanceMonitor
from core.data_utils import RTKPoint, PerceptionPoint, GeoUtils
from core.config_loader import Config


class TestRTKQueryOptimization(unittest.TestCase):
    """RTK查询优化测试"""
    
    def setUp(self):
        """测试初始化"""
        # 创建测试配置
        self.config = Config({
            'performance': {
                'enable_time_aligned_search': True,
                'rtk_max_time_diff': 3.0,
                'enable_performance_logging': True
            },
            'scoring': {
                'method': 'unified',
                'enable_score_cache': True,
                'pre_filter_threshold': 0.4
            },
            'matching': {
                'local_match_thr': 0.7,
                'min_segment_length': 2
            }
        })
        
        # 创建测试RTK点
        base_time = datetime.now()
        self.rtk_points = []
        for i in range(100):
            rtk_point = RTKPoint(
                timestamp=base_time + timedelta(seconds=i * 0.1),
                lat=39.9 + i * 0.0001,
                lon=116.3 + i * 0.0001,
                alt=50.0,
                accuracy=0.1
            )
            self.rtk_points.append(rtk_point)
        
        # 创建测试感知点
        self.perception_points = []
        for i in range(50):
            per_point = PerceptionPoint(
                timestamp=base_time + timedelta(seconds=i * 0.2),
                id=1,
                lat=39.9 + i * 0.0001 + 0.00001,  # 略微偏移
                lon=116.3 + i * 0.0001 + 0.00001,
                speed=10.0,
                heading=90.0
            )
            self.perception_points.append(per_point)
    
    def test_rtk_optimization_initialization(self):
        """测试RTK优化初始化"""
        matcher = SimpleDistanceMatcher(self.config, self.rtk_points)
        
        # 检查优化是否启用
        self.assertTrue(matcher.rtk_optimization_enabled)
        
        # 检查预处理数据结构
        self.assertEqual(len(matcher.rtk_timestamps), len(self.rtk_points))
        self.assertEqual(len(matcher.rtk_lats_rad), len(self.rtk_points))
        self.assertEqual(len(matcher.rtk_lons_rad), len(self.rtk_points))
        
        # 检查时间戳是否有序
        self.assertTrue(np.all(matcher.rtk_timestamps[:-1] <= matcher.rtk_timestamps[1:]))
    
    def test_optimized_vs_legacy_query(self):
        """测试优化查询与传统查询的结果一致性"""
        matcher = SimpleDistanceMatcher(self.config, self.rtk_points)
        
        # 测试多个感知点
        for per_point in self.perception_points[:10]:
            # 优化查询
            optimized_distance = matcher.find_nearest_rtk_distance_optimized(per_point)
            
            # 传统查询
            legacy_distance = matcher._find_nearest_rtk_distance_legacy(per_point)
            
            # 结果应该相近（允许小的数值误差）
            if optimized_distance != float('inf') and legacy_distance != float('inf'):
                self.assertAlmostEqual(optimized_distance, legacy_distance, places=2)
    
    def test_performance_improvement(self):
        """测试性能提升效果"""
        matcher = SimpleDistanceMatcher(self.config, self.rtk_points)
        
        # 测试优化查询性能
        start_time = time.perf_counter()
        for per_point in self.perception_points:
            matcher.find_nearest_rtk_distance_optimized(per_point)
        optimized_time = time.perf_counter() - start_time
        
        # 测试传统查询性能
        start_time = time.perf_counter()
        for per_point in self.perception_points:
            matcher._find_nearest_rtk_distance_legacy(per_point)
        legacy_time = time.perf_counter() - start_time
        
        # 优化查询应该更快（至少快2倍）
        self.assertLess(optimized_time * 2, legacy_time)
        print(f"性能提升: {legacy_time/optimized_time:.1f}×")


class TestScoreCache(unittest.TestCase):
    """评分缓存测试"""
    
    def setUp(self):
        """测试初始化"""
        self.config = Config({
            'scoring': {
                'enable_score_cache': True,
                'cache_debug_output': False
            }
        })
        self.cache = ScoreCache(self.config)
    
    def test_cache_basic_functionality(self):
        """测试缓存基本功能"""
        # 模拟评分函数
        def mock_scorer(segment):
            return 0.8
        
        # 模拟轨迹段
        mock_segment = Mock()
        mock_segment.id = "test_segment_1"
        
        # 第一次调用应该计算评分
        score1 = self.cache.get_score("test_segment_1", mock_segment, mock_scorer)
        self.assertEqual(score1, 0.8)
        self.assertEqual(self.cache._stats['misses'], 1)
        self.assertEqual(self.cache._stats['hits'], 0)
        
        # 第二次调用应该使用缓存
        score2 = self.cache.get_score("test_segment_1", mock_segment, mock_scorer)
        self.assertEqual(score2, 0.8)
        self.assertEqual(self.cache._stats['misses'], 1)
        self.assertEqual(self.cache._stats['hits'], 1)
    
    def test_cache_statistics(self):
        """测试缓存统计功能"""
        def mock_scorer(segment):
            return 0.5
        
        mock_segment = Mock()
        
        # 多次调用不同ID
        for i in range(5):
            mock_segment.id = f"segment_{i}"
            self.cache.get_score(f"segment_{i}", mock_segment, mock_scorer)
        
        # 重复调用相同ID
        for i in range(3):
            mock_segment.id = "segment_0"
            self.cache.get_score("segment_0", mock_segment, mock_scorer)
        
        stats = self.cache.get_stats()
        self.assertEqual(stats['total_requests'], 8)
        self.assertEqual(stats['cache_misses'], 5)
        self.assertEqual(stats['cache_hits'], 3)
        self.assertAlmostEqual(stats['hit_rate'], 3/8)
    
    def test_cache_disabled(self):
        """测试缓存禁用功能"""
        config = Config({'scoring': {'enable_score_cache': False}})
        cache = ScoreCache(config)
        
        call_count = 0
        def counting_scorer(segment):
            nonlocal call_count
            call_count += 1
            return 0.6
        
        mock_segment = Mock()
        mock_segment.id = "test_segment"
        
        # 多次调用相同ID，应该每次都计算
        for _ in range(3):
            cache.get_score("test_segment", mock_segment, counting_scorer)
        
        self.assertEqual(call_count, 3)  # 每次都应该调用评分函数


class TestPerformanceMonitor(unittest.TestCase):
    """性能监控测试"""
    
    def setUp(self):
        """测试初始化"""
        self.config = Config({
            'performance': {
                'enable_performance_logging': True
            }
        })
        self.monitor = PerformanceMonitor(self.config)
    
    def test_timing_functionality(self):
        """测试计时功能"""
        # 模拟RTK查询
        start_time = self.monitor.start_rtk_query_timer()
        time.sleep(0.001)  # 模拟查询耗时
        self.monitor.end_rtk_query_timer(start_time, optimized=True)
        
        self.assertEqual(self.monitor.rtk_query_count, 1)
        self.assertEqual(len(self.monitor.rtk_query_times), 1)
        self.assertGreater(self.monitor.rtk_query_times[0], 0)
    
    def test_performance_logging_disabled(self):
        """测试性能日志禁用"""
        config = Config({'performance': {'enable_performance_logging': False}})
        monitor = PerformanceMonitor(config)
        
        start_time = monitor.start_rtk_query_timer()
        self.assertIsNone(start_time)
        
        monitor.end_rtk_query_timer(start_time, optimized=True)
        self.assertEqual(monitor.rtk_query_count, 0)


if __name__ == '__main__':
    unittest.main()

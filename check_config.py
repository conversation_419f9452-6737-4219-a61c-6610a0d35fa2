#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查三种配置模式的实际参数
"""

import json
from core.config_loader import load_config

def check_configs():
    # 测试simple_distance配置
    config1 = load_config('config/unified_config.json', 'simple_distance')
    print('=== Simple Distance Mode ===')
    print(f'enable_time_aligned_search: {config1.get("performance.enable_time_aligned_search")}')
    print(f'enable_score_cache: {config1.get("scoring.enable_score_cache")}')
    print(f'rtk_max_time_diff: {config1.get("performance.rtk_max_time_diff")}')
    print(f'gap_match_thr: {config1.get("matching.gap_match_thr")}')
    print(f'rtk_buffer: {config1.get("matching.rtk_buffer")}')

    # 测试compatibility_mode配置
    config2 = load_config('config/unified_config.json', 'compatibility_mode')
    print('\n=== Compatibility Mode ===')
    print(f'enable_time_aligned_search: {config2.get("performance.enable_time_aligned_search")}')
    print(f'enable_score_cache: {config2.get("scoring.enable_score_cache")}')
    print(f'rtk_max_time_diff: {config2.get("performance.rtk_max_time_diff")}')
    print(f'gap_match_thr: {config2.get("matching.gap_match_thr")}')
    print(f'rtk_buffer: {config2.get("matching.rtk_buffer")}')

    # 测试performance_optimized配置
    config3 = load_config('config/unified_config.json', 'performance_optimized')
    print('\n=== Performance Optimized Mode ===')
    print(f'enable_time_aligned_search: {config3.get("performance.enable_time_aligned_search")}')
    print(f'enable_score_cache: {config3.get("scoring.enable_score_cache")}')
    print(f'rtk_max_time_diff: {config3.get("performance.rtk_max_time_diff")}')
    print(f'gap_match_thr: {config3.get("matching.gap_match_thr")}')
    print(f'rtk_buffer: {config3.get("matching.rtk_buffer")}')

    print('\n=== 关键差异分析 ===')
    print('1. RTK查询算法:')
    print(f'   simple_distance: {"二分查找优化" if config1.get("performance.enable_time_aligned_search") else "传统遍历"}')
    print(f'   compatibility_mode: {"二分查找优化" if config2.get("performance.enable_time_aligned_search") else "传统遍历"}')
    print(f'   performance_optimized: {"二分查找优化" if config3.get("performance.enable_time_aligned_search") else "传统遍历"}')
    
    print('\n2. 评分缓存:')
    print(f'   simple_distance: {"启用" if config1.get("scoring.enable_score_cache") else "禁用"}')
    print(f'   compatibility_mode: {"启用" if config2.get("scoring.enable_score_cache") else "禁用"}')
    print(f'   performance_optimized: {"启用" if config3.get("scoring.enable_score_cache") else "禁用"}')
    
    print('\n3. 时间窗口:')
    print(f'   simple_distance: {config1.get("performance.rtk_max_time_diff")}s')
    print(f'   compatibility_mode: {config2.get("performance.rtk_max_time_diff")}s')
    print(f'   performance_optimized: {config3.get("performance.rtk_max_time_diff")}s')

if __name__ == "__main__":
    check_configs()

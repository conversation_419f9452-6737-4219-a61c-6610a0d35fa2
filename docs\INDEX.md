# 📚 车路协同轨迹匹配系统 - 完整文档索引

> **🎯 项目状态**: 生产就绪 | **📊 版本**: v2.0 增强版 | **🔄 最后更新**: 2025-07-31

## 🌟 文档导航

### 📖 主要文档（根目录）
- [📄 README.md](../README.md) - **项目主文档** - 完整功能介绍和快速开始
- [🚀 QUICK_START.md](../QUICK_START.md) - **5分钟快速上手** - 新用户必读
- [📊 BATCH_PROCESSING_GUIDE.md](../BATCH_PROCESSING_GUIDE.md) - **批量处理指南** - 详细使用说明
- [📊 批量处理HTML报告使用指南.md](../批量处理HTML报告使用指南.md) - **HTML报告功能**
- [🏆 项目完成总结.md](../项目完成总结.md) - **项目成果总结**
- [🧹 项目清理与文档完善完成总结.md](../项目清理与文档完善完成总结.md) - **项目清理记录**

## 🎯 按用户类型分类

### 👤 新用户入门路径
1. **5分钟体验** → [🚀 QUICK_START.md](../QUICK_START.md)
2. **了解功能** → [📄 README.md](../README.md)
3. **批量处理** → [📊 BATCH_PROCESSING_GUIDE.md](../BATCH_PROCESSING_GUIDE.md)
4. **配置调优** → [⚙️ 配置参考手册](config_reference.md)

### 👨‍💻 开发者路径
1. **API参考** → [📚 API_REFERENCE.md](API_REFERENCE.md)
2. **最佳实践** → [📋 BEST_PRACTICES.md](BEST_PRACTICES.md)
3. **配置系统** → [⚙️ config_reference.md](config_reference.md)
4. **分析器开发** → [🔧 custom_analyzer_development_guide.md](custom_analyzer_development_guide.md)
5. **性能优化** → [🚀 performance_optimization_usage_guide.md](performance_optimization_usage_guide.md)

### 🔧 运维人员路径
1. **部署指南** → [📋 BEST_PRACTICES.md](BEST_PRACTICES.md)
2. **配置管理** → [⚙️ config_reference.md](config_reference.md)
3. **故障排除** → [📊 scoring_system_guide.md](scoring_system_guide.md)
4. **性能监控** → [📈 performance_optimization_plan.md](performance_optimization_plan.md)

## 📚 技术文档分类

### 🔧 核心功能文档
- [📚 API_REFERENCE.md](API_REFERENCE.md) - **API参考文档** - 核心模块API详细说明
- [📋 BEST_PRACTICES.md](BEST_PRACTICES.md) - **最佳实践指南** - 数据准备、配置优化、故障排除
- [⚙️ config_reference.md](config_reference.md) - **配置参考手册** - 完整配置参数说明
- [🔧 custom_analyzer_development_guide.md](custom_analyzer_development_guide.md) - **自定义分析器开发指南** - 完整开发教程

### 🎯 评分系统文档
- [🚀 quick_start_scoring.md](quick_start_scoring.md) - **统一评分快速上手** - 5分钟体验
- [📊 scoring_system_guide.md](scoring_system_guide.md) - **评分系统详细指南** - 完整使用说明
- [⚙️ config_fields_explanation.md](config_fields_explanation.md) - **配置字段说明** - 传统配置参数

### 🔍 专项功能文档
- [📊 gap_analysis_usage_example.md](gap_analysis_usage_example.md) - **间隙分析使用示例**
- [🧭 heading_angle_guide.md](heading_angle_guide.md) - **航向角处理指南**
- [⚡ speed_unit_guide.md](speed_unit_guide.md) - **速度单位处理指南**

### 🚀 性能优化文档
- [📈 performance_optimization_plan.md](performance_optimization_plan.md) - **性能优化计划**
- [🔧 performance_optimization_usage_guide.md](performance_optimization_usage_guide.md) - **性能优化使用指南**

## 🎮 功能演示

### 🚀 快速体验
```bash
# 1. 单文件处理演示
python main.py --rtk data/31.log --perception data/save_1753355915725.txt --config config/unified_config.json

# 2. 批量处理演示
python batch_simple.py --batch data/batch.csv --config config/unified_config.json

# 3. 增强HTML报告演示
python demo_enhanced_html_reports.py
```

### 📊 功能特性展示
- **轨迹匹配**: 高精度感知数据与RTK轨迹对比
- **批量处理**: 串行/并行处理，支持大规模数据
- **HTML报告**: 专业的可视化分析报告
- **异常检测**: 轨迹分裂、ID切换、漏检检测
- **精度分析**: 位置、速度、航向多维度评估

## 📊 项目统计

### 📁 文档统计
- **主要文档**: 6个（根目录）
- **技术文档**: 13个（docs目录）
- **总文档数**: 19个
- **文档覆盖**: 100%功能覆盖

### 🎯 功能覆盖
- ✅ **快速开始**: 5分钟上手指南
- ✅ **完整教程**: 详细使用说明
- ✅ **API参考**: 开发者文档
- ✅ **最佳实践**: 生产部署指南
- ✅ **故障排除**: 问题诊断方案

## 🔄 文档更新记录

### v2.0.0 (2025-07-31) - 文档整合版
- 🆕 **新增**: 统一文档索引和导航系统
- 🔄 **更新**: 重新组织文档结构和分类
- ✨ **优化**: 按用户类型提供阅读路径
- 📊 **完善**: 补充缺失的技术文档

### v1.5.0 (2025-07-30) - 增强HTML报告版
- 🆕 **新增**: 批量处理HTML报告功能
- 📊 **新增**: 详细的精度分析和异常检测
- 🔗 **新增**: 智能链接系统

### v1.0.0 - 基础功能版
- ✅ **完成**: 核心轨迹匹配功能
- ✅ **完成**: 基础批量处理系统
- ✅ **完成**: 配置系统和评分算法

## 💡 使用建议

### 🎯 推荐学习路径
1. **新手**: QUICK_START.md → README.md → BATCH_PROCESSING_GUIDE.md
2. **开发**: API_REFERENCE.md → BEST_PRACTICES.md → config_reference.md
3. **运维**: BEST_PRACTICES.md → config_reference.md → scoring_system_guide.md

### 📞 获取帮助
- 📖 **查看文档**: 按分类查找相关文档
- 🧪 **运行测试**: 使用演示脚本验证功能
- ⚙️ **检查配置**: 参考配置文档调整参数
- 📊 **查看日志**: 分析系统输出和错误信息

---

**🎯 开始您的车路协同轨迹匹配之旅！** 🚀

> 💡 **提示**: 建议从 [🚀 QUICK_START.md](../QUICK_START.md) 开始，5分钟即可体验完整功能！

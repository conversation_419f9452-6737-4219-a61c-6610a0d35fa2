# 🔧 自定义分析器开发指南

> **📚 如何为车路协同轨迹匹配系统添加自定义分析器** - 开发者专用指南

## 📋 概述

本指南将详细介绍如何为项目添加自定义分析器，包括分析器架构、开发步骤、最佳实践和完整示例。

## 🏗️ 分析器架构

### 🎯 核心组件

```
📦 异常分析框架
├── 🔧 BaseAnomalyAnalyzer      # 分析器基类
├── 📊 AnalysisResult           # 分析结果数据结构
├── 🎮 AnomalyAnalysisManager   # 分析器管理器
├── 📋 AnalyzerRegistry         # 分析器注册表
└── 🔍 内置分析器
    ├── SplitDetectionAnalyzer  # 分裂检测
    ├── IDSwitchAnalyzer        # ID切换检测
    ├── MissingGapAnalyzer      # 漏检分析
    └── AccuracyAnalyzer        # 精度分析
```

### 🎯 设计原则

- **插件化架构**: 支持动态注册和卸载分析器
- **统一接口**: 所有分析器继承相同基类
- **配置驱动**: 通过配置文件控制分析器行为
- **结果标准化**: 统一的分析结果格式
- **错误处理**: 完善的异常处理机制

## 🚀 快速开始

### 1. 创建基础分析器

```python
# 文件: core/anomaly_analysis/my_custom_analyzer.py

from typing import List, Any, Dict
from .base_analyzer import BaseAnomalyAnalyzer, AnalysisResult

class MyCustomAnalyzer(BaseAnomalyAnalyzer):
    """自定义分析器示例"""
    
    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        
        # 提取配置参数
        self.threshold = config.get('my_custom_threshold', 1.0)
        self.enable_advanced = config.get('my_custom_advanced', False)
        
        self.logger.info(f"自定义分析器初始化: threshold={self.threshold}")
    
    @property
    def analysis_type(self) -> str:
        """返回分析类型标识"""
        return "my_custom_analysis"
    
    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """执行分析的主要方法"""
        self.log_analysis_start(trajectory_chain, **kwargs)
        
        # 验证输入
        if not self.validate_input(trajectory_chain, **kwargs):
            return self.create_result(False, "输入数据验证失败")
        
        # 创建结果对象
        result = self.create_result()
        
        try:
            # 执行分析逻辑
            anomaly_count = 0
            
            for i, segment in enumerate(trajectory_chain):
                if self._detect_anomaly(segment):
                    event = {
                        'type': 'custom_anomaly',
                        'segment_index': i,
                        'timestamp': segment.get('timestamp'),
                        'description': '检测到自定义异常',
                        'severity': 'medium',
                        'confidence': 0.8
                    }
                    result.add_event(event)
                    anomaly_count += 1
            
            # 添加统计信息
            result.add_statistic('total_segments', len(trajectory_chain))
            result.add_statistic('anomaly_count', anomaly_count)
            result.add_statistic('anomaly_rate', anomaly_count / len(trajectory_chain))
            
            # 添加元数据
            result.add_metadata('threshold_used', self.threshold)
            result.add_metadata('advanced_mode', self.enable_advanced)
            
            self.log_analysis_complete(result)
            return result
            
        except Exception as e:
            self.logger.error(f"分析过程出错: {e}")
            return self.create_result(False, f"分析过程出错: {str(e)}")
    
    def _detect_anomaly(self, segment: Dict[str, Any]) -> bool:
        """自定义异常检测逻辑"""
        # 示例: 检测速度异常
        speed = segment.get('speed', 0)
        return speed > self.threshold * 10  # 简单示例
```

### 2. 注册分析器

#### 方式1: 静态注册（推荐）

```python
# 在 core/anomaly_analysis/__init__.py 中添加

from .my_custom_analyzer import MyCustomAnalyzer

# 更新 __all__ 列表
__all__ = [
    'BaseAnomalyAnalyzer',
    'AnalysisResult',
    'AnomalyAnalysisManager',
    # ... 其他分析器
    'MyCustomAnalyzer'  # 添加您的分析器
]
```

#### 方式2: 动态注册

```python
# 在使用时动态注册
from core.anomaly_analysis import AnomalyAnalysisManager
from core.anomaly_analysis.my_custom_analyzer import MyCustomAnalyzer

# 创建管理器
manager = AnomalyAnalysisManager(config)

# 注册自定义分析器
manager.register_analyzer(MyCustomAnalyzer, 'my_custom_analyzer')
```

### 3. 配置分析器

```json
{
  "enabled_analyzers": [
    "split_detection",
    "id_switch",
    "missing_gap",
    "my_custom_analyzer"
  ],
  "my_custom_threshold": 2.5,
  "my_custom_advanced": true,
  "my_custom_analyzer_enabled": true
}
```

## 📚 详细开发指南

### 🔧 基类接口详解

#### BaseAnomalyAnalyzer 核心方法

| 方法 | 用途 | 是否必须重写 |
|------|------|-------------|
| `__init__()` | 初始化分析器 | 建议重写 |
| `analyze()` | 执行分析逻辑 | **必须重写** |
| `analysis_type` | 返回分析类型 | **必须重写** |
| `validate_input()` | 验证输入数据 | 可选重写 |
| `is_enabled()` | 检查启用状态 | 无需重写 |

#### AnalysisResult 数据结构

```python
@dataclass
class AnalysisResult:
    analyzer_name: str              # 分析器名称
    analysis_type: str              # 分析类型
    timestamp: datetime             # 分析时间戳
    events: List[Dict[str, Any]]    # 检测到的事件列表
    statistics: Dict[str, Any]      # 统计信息
    metadata: Dict[str, Any]        # 元数据
    success: bool                   # 是否成功
    error_message: Optional[str]    # 错误信息
```

### 🎯 事件格式规范

#### 标准事件结构

```python
event = {
    'type': 'event_type',           # 事件类型（必须）
    'timestamp': '2024-01-01T10:00:00',  # 时间戳（建议）
    'segment_index': 0,             # 轨迹段索引（建议）
    'description': '事件描述',       # 事件描述（建议）
    'severity': 'low|medium|high',  # 严重程度（建议）
    'confidence': 0.8,              # 置信度 0-1（可选）
    'location': {                   # 位置信息（可选）
        'lat': 39.9042,
        'lon': 116.4074
    },
    'custom_data': {...}            # 自定义数据（可选）
}
```

#### 事件类型建议

| 类型 | 用途 | 示例 |
|------|------|------|
| `anomaly_*` | 异常检测 | `anomaly_speed`, `anomaly_direction` |
| `quality_*` | 质量评估 | `quality_low`, `quality_degraded` |
| `pattern_*` | 模式识别 | `pattern_unusual`, `pattern_suspicious` |
| `threshold_*` | 阈值检测 | `threshold_exceeded`, `threshold_below` |

### 🔍 高级功能

#### 1. 配置验证

```python
def _extract_common_config(self):
    """提取和验证配置参数"""
    # 必需参数验证
    required_params = ['my_custom_threshold']
    for param in required_params:
        if param not in self.config:
            raise ValueError(f"缺少必需配置参数: {param}")
    
    # 参数范围验证
    threshold = self.config.get('my_custom_threshold')
    if not 0 < threshold < 10:
        raise ValueError(f"threshold 参数超出有效范围 (0, 10): {threshold}")
    
    self.threshold = threshold
```

#### 2. 输入数据验证

```python
def validate_input(self, trajectory_chain: List[Any], **kwargs) -> bool:
    """自定义输入验证"""
    if not super().validate_input(trajectory_chain, **kwargs):
        return False
    
    # 检查数据完整性
    for segment in trajectory_chain:
        if 'speed' not in segment:
            self.logger.warning("轨迹段缺少速度信息")
            return False
    
    # 检查数据量
    if len(trajectory_chain) < 10:
        self.logger.warning("轨迹段数量过少，无法进行有效分析")
        return False
    
    return True
```

#### 3. 多阶段分析

```python
def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
    """多阶段分析示例"""
    result = self.create_result()
    
    try:
        # 阶段1: 预处理
        processed_data = self._preprocess_data(trajectory_chain)
        result.add_metadata('preprocessing_completed', True)
        
        # 阶段2: 特征提取
        features = self._extract_features(processed_data)
        result.add_statistic('feature_count', len(features))
        
        # 阶段3: 异常检测
        anomalies = self._detect_anomalies(features)
        
        # 阶段4: 结果后处理
        filtered_anomalies = self._filter_anomalies(anomalies)
        
        # 添加事件
        for anomaly in filtered_anomalies:
            result.add_event(anomaly)
        
        return result
        
    except Exception as e:
        return self.create_result(False, str(e))
```

## 🧪 测试指南

### 单元测试示例

```python
# 文件: tests/test_my_custom_analyzer.py

import unittest
from core.anomaly_analysis.my_custom_analyzer import MyCustomAnalyzer

class TestMyCustomAnalyzer(unittest.TestCase):
    
    def setUp(self):
        """测试初始化"""
        self.config = {
            'my_custom_threshold': 2.0,
            'my_custom_advanced': True
        }
        self.analyzer = MyCustomAnalyzer(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.analyzer.threshold, 2.0)
        self.assertTrue(self.analyzer.enable_advanced)
        self.assertEqual(self.analyzer.analysis_type, "my_custom_analysis")
    
    def test_analyze_empty_input(self):
        """测试空输入"""
        result = self.analyzer.analyze([])
        self.assertFalse(result.success)
        self.assertIn("验证失败", result.error_message)
    
    def test_analyze_normal_case(self):
        """测试正常情况"""
        trajectory_data = [
            {'timestamp': '2024-01-01T10:00:00', 'speed': 15.0},
            {'timestamp': '2024-01-01T10:00:01', 'speed': 16.0},
            {'timestamp': '2024-01-01T10:00:02', 'speed': 25.0}  # 异常
        ]
        
        result = self.analyzer.analyze(trajectory_data)
        self.assertTrue(result.success)
        self.assertEqual(result.get_event_count(), 1)
        
        event = result.events[0]
        self.assertEqual(event['type'], 'custom_anomaly')
        self.assertEqual(event['segment_index'], 2)

if __name__ == '__main__':
    unittest.main()
```

### 集成测试

```python
# 测试与分析管理器的集成
def test_integration_with_manager():
    config = {
        'enabled_analyzers': ['my_custom_analyzer'],
        'my_custom_threshold': 2.0
    }
    
    manager = AnomalyAnalysisManager(config)
    manager.register_analyzer(MyCustomAnalyzer, 'my_custom_analyzer')
    
    trajectory_data = [...]  # 测试数据
    results = manager.analyze_all(trajectory_data)
    
    assert 'my_custom_analyzer' in results
    assert results['my_custom_analyzer'].success
```

## 📋 最佳实践

### ✅ 推荐做法

1. **命名规范**
   - 类名: `XxxAnalyzer` 格式
   - 文件名: `xxx_analyzer.py` 格式
   - 配置前缀: `xxx_` 格式

2. **错误处理**
   ```python
   try:
       # 分析逻辑
       pass
   except SpecificException as e:
       self.logger.error(f"特定错误: {e}")
       return self.create_result(False, f"特定错误: {e}")
   except Exception as e:
       self.logger.error(f"未知错误: {e}")
       return self.create_result(False, f"未知错误: {e}")
   ```

3. **日志记录**
   ```python
   self.logger.info("开始分析")
   self.logger.debug(f"处理段 {i}: {segment}")
   self.logger.warning("检测到潜在问题")
   self.logger.error("分析失败")
   ```

4. **配置管理**
   ```python
   # 提供默认值
   self.threshold = config.get('threshold', 1.0)
   
   # 验证配置
   if self.threshold <= 0:
       raise ValueError("threshold 必须大于 0")
   ```

### ❌ 避免的做法

1. **不要硬编码参数** - 所有参数都应该可配置
2. **不要忽略异常** - 必须妥善处理所有可能的异常
3. **不要阻塞执行** - 避免长时间运行的操作
4. **不要修改输入数据** - 保持输入数据的不变性

## 🔗 相关资源

### 📚 参考文档
- [📚 API_REFERENCE.md](API_REFERENCE.md) - 完整API参考
- [⚙️ config_reference.md](config_reference.md) - 配置参考手册
- [📋 BEST_PRACTICES.md](BEST_PRACTICES.md) - 开发最佳实践

### 🎯 示例代码
- `core/anomaly_analysis/split_detector.py` - 分裂检测分析器
- `core/anomaly_analysis/id_switch_analyzer.py` - ID切换分析器
- `core/anomaly_analysis/missing_gap_analyzer.py` - 漏检分析器

### 🧪 测试参考
- `tests/test_anomaly_analysis.py` - 分析器测试示例
- `tests/performance_benchmark_test.py` - 性能测试

## 🎯 高级开发技巧

### 🔄 分析器链式调用

```python
class ChainableAnalyzer(BaseAnomalyAnalyzer):
    """支持链式调用的分析器"""

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self.next_analyzer = None

    def set_next(self, analyzer: BaseAnomalyAnalyzer):
        """设置下一个分析器"""
        self.next_analyzer = analyzer
        return analyzer

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """执行分析并传递给下一个分析器"""
        result = self._do_analysis(trajectory_chain, **kwargs)

        # 如果有下一个分析器，继续处理
        if self.next_analyzer and result.success:
            next_result = self.next_analyzer.analyze(trajectory_chain, **kwargs)
            # 合并结果
            result = self._merge_results(result, next_result)

        return result
```

### 📊 性能优化技巧

```python
class OptimizedAnalyzer(BaseAnomalyAnalyzer):
    """性能优化的分析器"""

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self.cache = {}
        self.batch_size = config.get('batch_size', 100)

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """批量处理优化"""
        result = self.create_result()

        # 批量处理
        for i in range(0, len(trajectory_chain), self.batch_size):
            batch = trajectory_chain[i:i + self.batch_size]
            batch_result = self._analyze_batch(batch)
            result.events.extend(batch_result.events)

        return result

    def _analyze_batch(self, batch: List[Any]) -> AnalysisResult:
        """批量分析实现"""
        # 使用缓存避免重复计算
        cache_key = self._generate_cache_key(batch)
        if cache_key in self.cache:
            return self.cache[cache_key]

        # 执行批量分析
        result = self._do_batch_analysis(batch)
        self.cache[cache_key] = result
        return result
```

### 🎮 交互式分析器

```python
class InteractiveAnalyzer(BaseAnomalyAnalyzer):
    """支持交互式分析的分析器"""

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """交互式分析"""
        result = self.create_result()

        # 支持实时回调
        callback = kwargs.get('progress_callback')
        total_segments = len(trajectory_chain)

        for i, segment in enumerate(trajectory_chain):
            # 分析当前段
            segment_result = self._analyze_segment(segment)
            if segment_result:
                result.add_event(segment_result)

            # 报告进度
            if callback:
                progress = (i + 1) / total_segments
                callback(progress, f"已处理 {i+1}/{total_segments} 个轨迹段")

        return result
```

## 🔧 部署和维护

### 📦 打包分析器

```python
# setup.py for custom analyzer package
from setuptools import setup, find_packages

setup(
    name="my-custom-analyzer",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "numpy>=1.20.0",
        "pandas>=1.3.0"
    ],
    entry_points={
        'trajectory_analyzers': [
            'my_custom = my_custom_analyzer:MyCustomAnalyzer'
        ]
    }
)
```

### 🔄 版本管理

```python
class VersionedAnalyzer(BaseAnomalyAnalyzer):
    """支持版本管理的分析器"""

    VERSION = "1.2.0"
    COMPATIBLE_VERSIONS = ["1.0.0", "1.1.0", "1.2.0"]

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self._check_compatibility()

    def _check_compatibility(self):
        """检查版本兼容性"""
        required_version = self.config.get('analyzer_version')
        if required_version and required_version not in self.COMPATIBLE_VERSIONS:
            raise ValueError(f"不兼容的分析器版本: {required_version}")

    def get_version_info(self) -> Dict[str, Any]:
        """获取版本信息"""
        return {
            'version': self.VERSION,
            'compatible_versions': self.COMPATIBLE_VERSIONS,
            'features': self._get_feature_list()
        }
```

### 📊 监控和指标

```python
class MonitoredAnalyzer(BaseAnomalyAnalyzer):
    """带监控功能的分析器"""

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self.metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'total_events_detected': 0,
            'average_processing_time': 0.0
        }

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        """带监控的分析"""
        import time
        start_time = time.time()

        try:
            result = self._do_analysis(trajectory_chain, **kwargs)

            # 更新指标
            self.metrics['total_analyses'] += 1
            if result.success:
                self.metrics['successful_analyses'] += 1
                self.metrics['total_events_detected'] += result.get_event_count()

            processing_time = time.time() - start_time
            self._update_average_time(processing_time)

            return result

        except Exception as e:
            self.metrics['total_analyses'] += 1
            raise e

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.metrics.copy()
```

## 🚀 实际应用示例

### 示例1: 速度异常检测器

```python
class SpeedAnomalyAnalyzer(BaseAnomalyAnalyzer):
    """速度异常检测分析器"""

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self.max_speed = config.get('max_speed_threshold', 60.0)  # km/h
        self.min_speed = config.get('min_speed_threshold', 0.0)
        self.speed_change_threshold = config.get('speed_change_threshold', 20.0)

    @property
    def analysis_type(self) -> str:
        return "speed_anomaly"

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        result = self.create_result()

        for i, segment in enumerate(trajectory_chain):
            speed = segment.get('speed', 0)

            # 检测超速
            if speed > self.max_speed:
                result.add_event({
                    'type': 'speed_exceeded',
                    'segment_index': i,
                    'timestamp': segment.get('timestamp'),
                    'speed': speed,
                    'threshold': self.max_speed,
                    'severity': 'high'
                })

            # 检测速度突变
            if i > 0:
                prev_speed = trajectory_chain[i-1].get('speed', 0)
                speed_change = abs(speed - prev_speed)
                if speed_change > self.speed_change_threshold:
                    result.add_event({
                        'type': 'speed_sudden_change',
                        'segment_index': i,
                        'timestamp': segment.get('timestamp'),
                        'speed_change': speed_change,
                        'threshold': self.speed_change_threshold,
                        'severity': 'medium'
                    })

        result.add_statistic('max_speed_detected', max(s.get('speed', 0) for s in trajectory_chain))
        result.add_statistic('avg_speed', sum(s.get('speed', 0) for s in trajectory_chain) / len(trajectory_chain))

        return result
```

### 示例2: 轨迹质量评估器

```python
class TrajectoryQualityAnalyzer(BaseAnomalyAnalyzer):
    """轨迹质量评估分析器"""

    def __init__(self, config: Dict[str, Any], name: str = None):
        super().__init__(config, name)
        self.min_points = config.get('min_trajectory_points', 10)
        self.max_time_gap = config.get('max_time_gap_seconds', 5.0)
        self.min_distance = config.get('min_movement_distance', 1.0)

    @property
    def analysis_type(self) -> str:
        return "trajectory_quality"

    def analyze(self, trajectory_chain: List[Any], **kwargs) -> AnalysisResult:
        result = self.create_result()

        # 检查轨迹长度
        if len(trajectory_chain) < self.min_points:
            result.add_event({
                'type': 'trajectory_too_short',
                'description': f'轨迹点数量不足: {len(trajectory_chain)} < {self.min_points}',
                'severity': 'high'
            })

        # 检查时间间隔
        time_gaps = self._calculate_time_gaps(trajectory_chain)
        large_gaps = [gap for gap in time_gaps if gap > self.max_time_gap]

        for i, gap in enumerate(large_gaps):
            result.add_event({
                'type': 'large_time_gap',
                'segment_index': i,
                'time_gap': gap,
                'threshold': self.max_time_gap,
                'severity': 'medium'
            })

        # 计算质量分数
        quality_score = self._calculate_quality_score(trajectory_chain, time_gaps)
        result.add_statistic('quality_score', quality_score)
        result.add_statistic('avg_time_gap', sum(time_gaps) / len(time_gaps) if time_gaps else 0)

        return result

    def _calculate_time_gaps(self, trajectory_chain: List[Any]) -> List[float]:
        """计算时间间隔"""
        gaps = []
        for i in range(1, len(trajectory_chain)):
            # 假设时间戳格式处理
            # 实际实现需要根据具体时间戳格式调整
            gaps.append(1.0)  # 简化示例
        return gaps

    def _calculate_quality_score(self, trajectory_chain: List[Any], time_gaps: List[float]) -> float:
        """计算轨迹质量分数 (0-1)"""
        score = 1.0

        # 长度惩罚
        if len(trajectory_chain) < self.min_points:
            score *= 0.5

        # 时间间隔惩罚
        if time_gaps:
            avg_gap = sum(time_gaps) / len(time_gaps)
            if avg_gap > self.max_time_gap:
                score *= 0.7

        return max(0.0, min(1.0, score))
```

## 📋 故障排除

### 常见问题及解决方案

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 分析器未被调用 | 未正确注册或配置 | 检查 `enabled_analyzers` 配置 |
| 分析结果为空 | 输入验证失败 | 重写 `validate_input` 方法 |
| 性能问题 | 算法复杂度过高 | 使用批量处理或缓存 |
| 内存泄漏 | 缓存未清理 | 实现缓存大小限制 |
| 配置错误 | 参数验证不足 | 加强配置验证逻辑 |

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('core.anomaly_analysis').setLevel(logging.DEBUG)

# 使用调试模式
analyzer = MyCustomAnalyzer(config)
analyzer.logger.setLevel(logging.DEBUG)

# 添加断点调试
import pdb; pdb.set_trace()
```

---

**🔧 开发高质量分析器，提升系统分析能力！**

> 💡 **提示**: 建议先阅读现有分析器代码，理解架构后再开始开发
>
> 📚 **下一步**: 查看 [API_REFERENCE.md](API_REFERENCE.md) 了解完整API，或参考 [BEST_PRACTICES.md](BEST_PRACTICES.md) 学习开发规范

#!/usr/bin/env python3
"""
批量轨迹匹配处理工具
基于现有的trajectory_matcher实现批量处理功能
"""

import sys
import os
import csv
import argparse
import json
from pathlib import Path
from datetime import datetime
import logging
from typing import List, Dict, Tuple

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# 导入现有的轨迹匹配器
import core.trajectory_matcher as tm
from batch_report_generator import BatchReportGenerator


class BatchTask:
    """批量处理任务"""
    def __init__(self, task_id: str, perception_file: str, rtk_file: str):
        self.task_id = task_id
        self.perception_file = perception_file
        self.rtk_file = rtk_file
        self.status = "pending"
        self.start_time = None
        self.end_time = None
        self.error_message = None
        self.output_dir = None


class SimpleBatchProcessor:
    """简化版批量处理器"""
    
    def __init__(self, config_path: str = "config/unified_config.json"):
        self.config_path = config_path
        self.tasks: List[BatchTask] = []
        self.results: List[Dict] = []
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('batch_processing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def parse_batch_file(self, batch_file: str) -> List[BatchTask]:
        """解析batch.csv文件"""
        tasks = []
        
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for i, row in enumerate(reader):
                    # 验证必要字段
                    if 'test_file' not in row or 'rtk_file' not in row:
                        self.logger.warning(f"第{i+1}行缺少必要字段，跳过")
                        continue
                    
                    perception_file = row['test_file'].strip()
                    rtk_file = row['rtk_file'].strip()
                    
                    # 验证文件存在
                    if not Path(perception_file).exists():
                        self.logger.warning(f"感知文件不存在: {perception_file}")
                        continue
                    
                    if not Path(rtk_file).exists():
                        self.logger.warning(f"RTK文件不存在: {rtk_file}")
                        continue
                    
                    # 创建任务
                    task_id = f"task_{i+1:03d}"
                    task = BatchTask(task_id, perception_file, rtk_file)
                    tasks.append(task)
                    
                    self.logger.info(f"添加任务 {task_id}: {perception_file} -> {rtk_file}")
        
        except Exception as e:
            self.logger.error(f"解析batch文件失败: {e}")
            raise
        
        return tasks
    
    def execute_single_task(self, task: BatchTask, output_base_dir: str) -> Dict:
        """执行单个任务"""
        task.start_time = datetime.now()
        task.status = "running"
        
        # 创建任务专用输出目录
        perception_name = Path(task.perception_file).stem
        rtk_name = Path(task.rtk_file).stem
        task_output_dir = Path(output_base_dir) / f"{perception_name}_vs_{rtk_name}"
        task_output_dir.mkdir(parents=True, exist_ok=True)
        task.output_dir = str(task_output_dir)
        
        try:
            self.logger.info(f"开始执行任务 {task.task_id}")
            
            # 备份原始sys.argv
            original_argv = sys.argv
            
            # 构建trajectory_matcher参数
            tm_args = [
                'trajectory_matcher.py',
                '--rtk', task.rtk_file,
                '--perception', task.perception_file,
                '--config', self.config_path,
                '--output-dir', task.output_dir
            ]
            
            # 设置新的argv并调用trajectory_matcher
            sys.argv = tm_args
            tm.main()
            
            # 恢复原始argv
            sys.argv = original_argv
            
            task.status = "completed"
            task.end_time = datetime.now()
            
            # 收集结果信息
            result = {
                'task_id': task.task_id,
                'perception_file': task.perception_file,
                'rtk_file': task.rtk_file,
                'status': task.status,
                'start_time': task.start_time.isoformat(),
                'end_time': task.end_time.isoformat(),
                'duration': (task.end_time - task.start_time).total_seconds(),
                'output_dir': task.output_dir
            }
            
            self.logger.info(f"任务 {task.task_id} 完成，耗时 {result['duration']:.2f}秒")
            return result
            
        except Exception as e:
            task.status = "failed"
            task.end_time = datetime.now()
            task.error_message = str(e)
            
            result = {
                'task_id': task.task_id,
                'perception_file': task.perception_file,
                'rtk_file': task.rtk_file,
                'status': task.status,
                'start_time': task.start_time.isoformat() if task.start_time else None,
                'end_time': task.end_time.isoformat(),
                'error_message': task.error_message,
                'output_dir': task.output_dir
            }
            
            self.logger.error(f"任务 {task.task_id} 失败: {e}")
            return result
    
    def process_batch(self, batch_file: str, output_dir: str = "output/batch_results") -> Dict:
        """处理批量任务"""
        self.logger.info("开始批量处理")
        
        # 解析任务
        self.tasks = self.parse_batch_file(batch_file)
        if not self.tasks:
            raise ValueError("没有有效的任务需要处理")
        
        self.logger.info(f"共解析到 {len(self.tasks)} 个任务")
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 执行任务
        self.results = []
        for i, task in enumerate(self.tasks, 1):
            self.logger.info(f"处理进度: {i}/{len(self.tasks)}")
            result = self.execute_single_task(task, output_dir)
            self.results.append(result)
        
        # 生成汇总报告
        summary = self.generate_summary(output_dir)
        
        self.logger.info("批量处理完成")
        return summary
    
    def generate_summary(self, output_dir: str) -> Dict:
        """生成汇总报告"""
        completed_tasks = [r for r in self.results if r['status'] == 'completed']
        failed_tasks = [r for r in self.results if r['status'] == 'failed']

        summary = {
            'total_tasks': len(self.results),
            'completed_tasks': len(completed_tasks),
            'failed_tasks': len(failed_tasks),
            'success_rate': len(completed_tasks) / len(self.results) * 100 if self.results else 0,
            'total_duration': sum(r.get('duration', 0) for r in self.results),
            'average_duration': sum(r.get('duration', 0) for r in completed_tasks) / len(completed_tasks) if completed_tasks else 0,
            'results': self.results
        }

        # 保存JSON报告
        summary_file = Path(output_dir) / "batch_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # 保存CSV报告
        csv_file = Path(output_dir) / "batch_summary.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if self.results:
                fieldnames = ['task_id', 'perception_file', 'rtk_file', 'status',
                            'start_time', 'end_time', 'duration', 'error_message', 'output_dir']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for result in self.results:
                    row = {k: result.get(k, '') for k in fieldnames}
                    writer.writerow(row)

        # 生成HTML报告
        try:
            html_file = Path(output_dir) / "batch_summary_report.html"
            report_generator = BatchReportGenerator()
            report_generator.generate_html_report(summary, str(html_file))
            self.logger.info(f"HTML报告已保存: {html_file}")
        except Exception as e:
            self.logger.warning(f"生成HTML报告失败: {e}")

        self.logger.info(f"汇总报告已保存: {summary_file}")
        return summary


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="批量轨迹匹配处理工具")
    parser.add_argument('--batch', required=True, help='批量任务文件路径 (batch.csv)')
    parser.add_argument('--config', default='config/unified_config.json', help='配置文件路径')
    parser.add_argument('--output', default='output/batch_results', help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.batch).exists():
        print(f"错误：批量任务文件不存在: {args.batch}")
        sys.exit(1)
    
    if not Path(args.config).exists():
        print(f"错误：配置文件不存在: {args.config}")
        sys.exit(1)
    
    try:
        # 创建批量处理器
        processor = SimpleBatchProcessor(args.config)
        
        # 执行批量处理
        summary = processor.process_batch(args.batch, args.output)
        
        # 输出结果
        print(f"\n[SUCCESS] 批量处理完成！")
        print(f"总任务数: {summary['total_tasks']}")
        print(f"成功任务: {summary['completed_tasks']}")
        print(f"失败任务: {summary['failed_tasks']}")
        print(f"总耗时: {summary['total_duration']:.2f}秒")
        print(f"输出目录: {args.output}")

    except Exception as e:
        print(f"[ERROR] 批量处理失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

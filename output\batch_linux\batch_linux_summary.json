{"total_tasks": 6, "completed_tasks": 6, "failed_tasks": 0, "success_rate": 100.0, "total_duration": 5.287082000000001, "wall_clock_time": 2.164583444595337, "parallel_efficiency": 2.4425401631898773, "average_duration": 0.8811803333333335, "processes_used": 4, "platform": "Windows", "cpu_count": 16, "results": [{"task_id": "task_001", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\31.log", "status": "completed", "start_time": "2025-07-31T13:02:17.903235", "end_time": "2025-07-31T13:02:18.917806", "duration": 1.014571, "output_dir": "output\\batch_linux\\save_1753355915725_vs_31", "process_id": 81236}, {"task_id": "task_002", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\32.log", "status": "completed", "start_time": "2025-07-31T13:02:18.116187", "end_time": "2025-07-31T13:02:19.051649", "duration": 0.935462, "output_dir": "output\\batch_linux\\save_1753355915725_vs_32", "process_id": 93928}, {"task_id": "task_003", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\33.log", "status": "completed", "start_time": "2025-07-31T13:02:18.448612", "end_time": "2025-07-31T13:02:19.527513", "duration": 1.078901, "output_dir": "output\\batch_linux\\save_1753355915725_vs_33", "process_id": 77628}, {"task_id": "task_005", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\35.log", "status": "completed", "start_time": "2025-07-31T13:02:18.917806", "end_time": "2025-07-31T13:02:19.555038", "duration": 0.637232, "output_dir": "output\\batch_linux\\save_1753355915725_vs_35", "process_id": 81236}, {"task_id": "task_004", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\34.log", "status": "completed", "start_time": "2025-07-31T13:02:18.555771", "end_time": "2025-07-31T13:02:19.593260", "duration": 1.037489, "output_dir": "output\\batch_linux\\save_1753355915725_vs_34", "process_id": 4040}, {"task_id": "task_006", "perception_file": ".\\data\\save_1753355915725.txt", "rtk_file": ".\\data\\36.log", "status": "completed", "start_time": "2025-07-31T13:02:19.051649", "end_time": "2025-07-31T13:02:19.635076", "duration": 0.583427, "output_dir": "output\\batch_linux\\save_1753355915725_vs_36", "process_id": 93928}]}
#!/usr/bin/env python3
"""
真实数据性能测试
使用指定的RTK和感知数据测试性能优化效果
"""

import sys
import os
import time
import json
from datetime import datetime
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.simple_distance_matcher import SimpleDistanceMatcher
from core.config_loader import Config
from core.data_utils import DataLoader
from core.preprocessor import RawDataPreprocessor


class RealDataPerformanceTest:
    """真实数据性能测试类"""
    
    def __init__(self, rtk_file, perception_file):
        self.rtk_file = rtk_file
        self.perception_file = perception_file
        self.results = {
            'test_info': {
                'rtk_file': rtk_file,
                'perception_file': perception_file,
                'timestamp': datetime.now().isoformat()
            },
            'data_stats': {},
            'performance_tests': [],
            'comparison': {}
        }
    
    def load_test_data(self):
        """加载测试数据"""
        print(f"加载测试数据...")
        print(f"RTK文件: {self.rtk_file}")
        print(f"感知文件: {self.perception_file}")

        # 创建预处理器
        preprocessor = RawDataPreprocessor()

        # 检测文件格式
        rtk_format = preprocessor.detect_file_format(self.rtk_file)
        perception_format = preprocessor.detect_file_format(self.perception_file)
        print(f"RTK文件格式: {rtk_format}")
        print(f"感知文件格式: {perception_format}")

        # 预处理文件（如果需要）
        rtk_csv_path = self.rtk_file
        perception_csv_path = self.perception_file

        if rtk_format != 'csv':
            print(f"预处理RTK文件...")
            rtk_csv_path = preprocessor.preprocess_rtk_file(self.rtk_file, "temp_rtk.csv")

        if perception_format != 'csv':
            print(f"预处理感知文件...")
            perception_csv_path = preprocessor.preprocess_perception_file(self.perception_file, "temp_perception.csv")

        # 创建数据加载器
        config = Config({})  # 使用默认配置
        data_loader = DataLoader(config)

        # 加载RTK数据
        start_time = time.perf_counter()
        self.rtk_points = data_loader.load_rtk_csv(rtk_csv_path)
        rtk_load_time = time.perf_counter() - start_time

        # 加载感知数据
        start_time = time.perf_counter()
        self.perception_points = data_loader.load_perception_csv(perception_csv_path)
        perception_load_time = time.perf_counter() - start_time
        
        # 统计数据信息
        self.results['data_stats'] = {
            'rtk_points_count': len(self.rtk_points),
            'perception_points_count': len(self.perception_points),
            'rtk_load_time': rtk_load_time,
            'perception_load_time': perception_load_time
        }
        
        print(f"✅ 数据加载完成:")
        print(f"  RTK点数: {len(self.rtk_points)}")
        print(f"  感知点数: {len(self.perception_points)}")
        print(f"  RTK加载时间: {rtk_load_time:.3f}s")
        print(f"  感知加载时间: {perception_load_time:.3f}s")
        
        # 数据时间范围分析
        if self.rtk_points:
            rtk_start = min(point.timestamp for point in self.rtk_points)
            rtk_end = max(point.timestamp for point in self.rtk_points)
            rtk_duration = (rtk_end - rtk_start).total_seconds()
            print(f"  RTK时间范围: {rtk_duration:.1f}s ({rtk_start} ~ {rtk_end})")
        
        if self.perception_points:
            per_start = min(point.timestamp for point in self.perception_points)
            per_end = max(point.timestamp for point in self.perception_points)
            per_duration = (per_end - per_start).total_seconds()
            print(f"  感知时间范围: {per_duration:.1f}s ({per_start} ~ {per_end})")
    
    def create_config(self, optimized=True):
        """创建配置"""
        if optimized:
            # 优化配置
            return Config({
                'performance': {
                    'enable_time_aligned_search': True,
                    'rtk_max_time_diff': 3.0,
                    'enable_performance_logging': True
                },
                'scoring': {
                    'method': 'unified',
                    'enable_score_cache': True,
                    'pre_filter_threshold': 0.4,
                    'cache_debug_output': False
                },
                'matching': {
                    'local_match_thr': 0.7,
                    'min_segment_length': 2
                },
                'corridor': {
                    'enable_corridor_filter': False
                }
            })
        else:
            # 传统配置（禁用优化）
            return Config({
                'performance': {
                    'enable_time_aligned_search': False,
                    'rtk_max_time_diff': 3.0,
                    'enable_performance_logging': False
                },
                'scoring': {
                    'method': 'unified',
                    'enable_score_cache': False,
                    'pre_filter_threshold': 0.0,
                    'cache_debug_output': False
                },
                'matching': {
                    'local_match_thr': 0.7,
                    'min_segment_length': 2
                },
                'corridor': {
                    'enable_corridor_filter': False
                }
            })
    
    def run_single_test(self, config, test_name):
        """运行单次测试"""
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print(f"{'='*60}")
        
        # 创建匹配器
        start_time = time.perf_counter()
        matcher = SimpleDistanceMatcher(config, self.rtk_points)
        init_time = time.perf_counter() - start_time
        
        # 阶段1: 感知点过滤
        start_time = time.perf_counter()
        filtered_perception = matcher.filter_perception_points(self.perception_points)
        filter_time = time.perf_counter() - start_time
        
        # 阶段2: 构建轨迹段
        start_time = time.perf_counter()
        segments = matcher.build_segments(filtered_perception)
        segments_time = time.perf_counter() - start_time
        
        # 阶段3: 构建核心链（关键优化阶段）
        start_time = time.perf_counter()
        core_chain = matcher.build_core_chain(segments)
        core_chain_time = time.perf_counter() - start_time
        
        # 总时间
        total_time = init_time + filter_time + segments_time + core_chain_time
        
        # 获取性能统计
        performance_stats = {}
        cache_stats = {}

        if hasattr(matcher, 'performance_monitor'):
            monitor = matcher.performance_monitor
            if monitor.enabled and monitor.rtk_query_times:
                avg_time = sum(monitor.rtk_query_times) / len(monitor.rtk_query_times) * 1000  # 转换为毫秒
                performance_stats = {
                    'rtk_query_count': monitor.rtk_query_count,
                    'optimized_query_count': monitor.rtk_query_count if monitor.optimization_enabled else 0,
                    'avg_query_time': avg_time,
                    'total_query_time': sum(monitor.rtk_query_times),
                    'performance_improvement': 1.0  # 默认值
                }

        if hasattr(matcher, 'score_cache'):
            cache_stats = matcher.score_cache.get_stats()
        
        # 结果统计
        result = {
            'test_name': test_name,
            'timing': {
                'init_time': init_time,
                'filter_time': filter_time,
                'segments_time': segments_time,
                'core_chain_time': core_chain_time,
                'total_time': total_time
            },
            'data_flow': {
                'input_perception_points': len(self.perception_points),
                'filtered_perception_points': len(filtered_perception),
                'segments_count': len(segments),
                'core_chain_count': len(core_chain)
            },
            'performance_stats': performance_stats,
            'cache_stats': cache_stats
        }
        
        # 打印结果
        print(f"⏱️  执行时间:")
        print(f"  初始化: {init_time:.3f}s")
        print(f"  感知过滤: {filter_time:.3f}s")
        print(f"  轨迹段构建: {segments_time:.3f}s")
        print(f"  核心链构建: {core_chain_time:.3f}s")
        print(f"  总时间: {total_time:.3f}s")
        
        print(f"\n📊 数据流:")
        print(f"  输入感知点: {len(self.perception_points)}")
        print(f"  过滤后感知点: {len(filtered_perception)}")
        print(f"  轨迹段数: {len(segments)}")
        print(f"  核心链段数: {len(core_chain)}")
        
        if performance_stats:
            print(f"\n🚀 RTK查询性能:")
            print(f"  总查询次数: {performance_stats.get('rtk_query_count', 0)}")
            print(f"  优化查询次数: {performance_stats.get('optimized_query_count', 0)}")
            print(f"  平均查询时间: {performance_stats.get('avg_query_time', 0):.3f}ms")
            if performance_stats.get('performance_improvement', 0) > 1:
                print(f"  性能提升: {performance_stats.get('performance_improvement', 1):.1f}×")
        
        if cache_stats:
            print(f"\n💾 评分缓存:")
            print(f"  缓存命中率: {cache_stats.get('hit_rate', 0):.1%}")
            print(f"  缓存大小: {cache_stats.get('cache_size', 0)}")
            print(f"  总请求数: {cache_stats.get('total_requests', 0)}")
        
        return result
    
    def run_comparison_test(self):
        """运行对比测试"""
        print(f"\n🔬 开始性能对比测试")
        
        # 测试1: 传统算法
        legacy_config = self.create_config(optimized=False)
        legacy_result = self.run_single_test(legacy_config, "传统算法")
        
        # 测试2: 优化算法
        optimized_config = self.create_config(optimized=True)
        optimized_result = self.run_single_test(optimized_config, "优化算法")
        
        # 保存结果
        self.results['performance_tests'] = [legacy_result, optimized_result]
        
        # 对比分析
        legacy_total = legacy_result['timing']['total_time']
        optimized_total = optimized_result['timing']['total_time']
        
        legacy_core = legacy_result['timing']['core_chain_time']
        optimized_core = optimized_result['timing']['core_chain_time']
        
        total_improvement = legacy_total / optimized_total if optimized_total > 0 else 1
        core_improvement = legacy_core / optimized_core if optimized_core > 0 else 1
        
        self.results['comparison'] = {
            'total_time_improvement': total_improvement,
            'core_chain_improvement': core_improvement,
            'time_saved': legacy_total - optimized_total,
            'core_time_saved': legacy_core - optimized_core,
            'target_achieved': optimized_total <= 7.0  # 目标是5-7秒
        }
        
        # 打印对比结果
        print(f"\n{'='*60}")
        print(f"📈 性能对比结果")
        print(f"{'='*60}")
        print(f"总执行时间:")
        print(f"  传统算法: {legacy_total:.3f}s")
        print(f"  优化算法: {optimized_total:.3f}s")
        print(f"  性能提升: {total_improvement:.1f}×")
        print(f"  节省时间: {legacy_total - optimized_total:.3f}s")
        
        print(f"\n核心链构建时间:")
        print(f"  传统算法: {legacy_core:.3f}s")
        print(f"  优化算法: {optimized_core:.3f}s")
        print(f"  性能提升: {core_improvement:.1f}×")
        print(f"  节省时间: {legacy_core - optimized_core:.3f}s")
        
        print(f"\n🎯 目标达成情况:")
        target_status = "✅ 已达成" if self.results['comparison']['target_achieved'] else "❌ 未达成"
        print(f"  目标: 总时间 ≤ 7s")
        print(f"  实际: {optimized_total:.3f}s")
        print(f"  状态: {target_status}")
        
        return self.results['comparison']
    
    def save_results(self, output_file="tests/real_data_test_results.json"):
        """保存测试结果"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n💾 测试结果已保存到: {output_file}")


def main():
    """主函数"""
    rtk_file = "./data/rtk_part005.txt"
    perception_file = "./data/AJ06993PAJ00115B1.txt"
    
    print("🚀 真实数据性能测试")
    print(f"RTK数据: {rtk_file}")
    print(f"感知数据: {perception_file}")
    
    # 检查文件是否存在
    if not os.path.exists(rtk_file):
        print(f"❌ RTK文件不存在: {rtk_file}")
        return
    
    if not os.path.exists(perception_file):
        print(f"❌ 感知文件不存在: {perception_file}")
        return
    
    # 创建测试实例
    test = RealDataPerformanceTest(rtk_file, perception_file)
    
    try:
        # 加载数据
        test.load_test_data()
        
        # 运行对比测试
        comparison = test.run_comparison_test()
        
        # 保存结果
        test.save_results()
        
        print(f"\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()

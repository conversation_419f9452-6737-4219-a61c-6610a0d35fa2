#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三种配置模式详细对比分析
"""

import pandas as pd
import os
import json

def analyze_mode_results():
    modes = ['simple_distance', 'performance_optimized', 'compatibility_mode']
    mode_names = {
        'simple_distance': '简单距离模式',
        'performance_optimized': '性能优化模式', 
        'compatibility_mode': '兼容模式'
    }
    
    results = {}
    
    print('=' * 100)
    print('🚀 车路协同轨迹匹配系统 - 三种配置模式详细对比分析')
    print('=' * 100)
    
    for mode in modes:
        csv_file = f'output/{mode}/rtk_part005_AJ06993PAJ00115B1_trajectory_matched.csv'
        json_file = f'output/{mode}/rtk_part005_AJ06993PAJ00115B1_diagnostic.json'
        
        if os.path.exists(csv_file) and os.path.exists(json_file):
            # 读取CSV数据
            df = pd.read_csv(csv_file)
            
            # 读取JSON诊断数据
            with open(json_file, 'r', encoding='utf-8') as f:
                diag_data = json.load(f)
            
            # 计算统计信息
            total_points = len(df)
            avg_distance_error = df['distance_error'].mean() if 'distance_error' in df.columns else 0
            avg_final_score = df['final_score'].mean() if 'final_score' in df.columns else 0
            avg_position_error = df['position_error_m'].mean() if 'position_error_m' in df.columns else 0
            avg_speed_error = df['speed_abs_error_ms'].mean() if 'speed_abs_error_ms' in df.columns else 0
            avg_heading_error = df['heading_error_deg'].mean() if 'heading_error_deg' in df.columns else 0
            
            # 从诊断数据获取信息
            coverage_rate = diag_data['statistics']['coverage_rate']
            rtk_duration = diag_data['metadata']['rtk_duration']
            matched_duration = diag_data['metadata']['matched_duration']
            
            results[mode] = {
                'total_points': total_points,
                'avg_final_score': avg_final_score,
                'avg_distance_error': avg_distance_error,
                'avg_position_error': avg_position_error,
                'avg_speed_error': avg_speed_error,
                'avg_heading_error': avg_heading_error,
                'coverage_rate': coverage_rate,
                'rtk_duration': rtk_duration,
                'matched_duration': matched_duration
            }
            
            print(f'\n📊 {mode_names[mode]}:')
            print(f'  ├─ 匹配点数: {total_points}')
            print(f'  ├─ 平均最终分数: {avg_final_score:.3f}')
            print(f'  ├─ 平均距离误差: {avg_distance_error:.3f} 米')
            print(f'  ├─ 平均位置误差: {avg_position_error:.3f} 米')
            print(f'  ├─ 平均速度误差: {avg_speed_error:.3f} m/s')
            print(f'  ├─ 平均航向误差: {avg_heading_error:.3f} 度')
            print(f'  ├─ 覆盖率: {coverage_rate:.1f}%')
            print(f'  ├─ RTK轨迹时长: {rtk_duration:.1f}s')
            print(f'  └─ 匹配轨迹时长: {matched_duration:.1f}s')
    
    # 对比分析
    print('\n' + '=' * 100)
    print('🔍 对比分析结果')
    print('=' * 100)
    
    if len(results) >= 2:
        # 找出最佳性能指标
        best_score_mode = max(results.keys(), key=lambda x: results[x]['avg_final_score'])
        best_accuracy_mode = min(results.keys(), key=lambda x: results[x]['avg_position_error'])
        
        print(f'🏆 最高匹配分数: {mode_names[best_score_mode]} ({results[best_score_mode]["avg_final_score"]:.3f})')
        print(f'🎯 最高位置精度: {mode_names[best_accuracy_mode]} ({results[best_accuracy_mode]["avg_position_error"]:.3f}m)')
        
        # 分析差异
        scores = [results[mode]['avg_final_score'] for mode in results.keys()]
        position_errors = [results[mode]['avg_position_error'] for mode in results.keys()]
        
        score_diff = max(scores) - min(scores)
        position_diff = max(position_errors) - min(position_errors)
        
        print(f'📊 匹配分数差异: {score_diff:.3f}')
        print(f'📏 位置误差差异: {position_diff:.3f}m')
        
        print('\n💡 使用建议:')
        if score_diff > 0.1:
            print('  ⚠️  不同模式间存在显著差异，建议根据具体需求选择:')
            print(f'     • 追求最高精度 → {mode_names[best_accuracy_mode]}')
            print(f'     • 追求最高匹配分数 → {mode_names[best_score_mode]}')
            print('     • 追求处理速度 → 性能优化模式')
        else:
            print('  ✅ 不同模式间差异较小，建议选择:')
            print('     • 日常使用 → 简单距离模式')
            print('     • 大批量处理 → 性能优化模式')
            print('     • 研究分析 → 兼容模式')
    
    # 配置建议
    print('\n' + '=' * 100)
    print('⚙️ 配置模式使用指南')
    print('=' * 100)
    
    print('🔧 simple_distance (简单距离模式):')
    print('   特点: 轻量级、快速处理')
    print('   适用: 数据质量好、快速验证、资源受限环境')
    print('   命令: --profile simple_distance')
    
    print('\n⚡ performance_optimized (性能优化模式):')
    print('   特点: 速度优先、智能缓存、时间对齐搜索')
    print('   适用: 大数据量处理、生产环境、批量处理')
    print('   命令: --profile performance_optimized')
    
    print('\n🎯 compatibility_mode (兼容模式):')
    print('   特点: 精度优先、传统算法、无缓存')
    print('   适用: 最高精度要求、复杂数据、研究分析')
    print('   命令: --profile compatibility_mode')

if __name__ == "__main__":
    analyze_mode_results()

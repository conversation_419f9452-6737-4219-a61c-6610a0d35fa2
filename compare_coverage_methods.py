#!/usr/bin/env python3
"""
比较新旧覆盖率计算方法的脚本
分析现有诊断文件中的数据，展示两种方法的差异
"""

import json
import os
from datetime import datetime
from pathlib import Path


def parse_timestamp(timestamp_str):
    """解析时间戳字符串"""
    try:
        # 处理带时区的时间戳
        if '+' in timestamp_str:
            timestamp_str = timestamp_str.split('+')[0]
        return datetime.fromisoformat(timestamp_str)
    except:
        return None


def calculate_old_coverage(rtk_duration, matched_duration):
    """旧版本覆盖率计算"""
    if rtk_duration > 0:
        return round((matched_duration / rtk_duration) * 100, 2)
    return 0


def calculate_new_coverage(rtk_start, rtk_end, perception_start, perception_end, matched_duration):
    """新版本覆盖率计算（时间对齐）"""
    # 计算对齐的时间范围
    aligned_start = min(rtk_start, perception_start)
    aligned_end = max(rtk_end, perception_end)
    aligned_duration = (aligned_end - aligned_start).total_seconds()
    
    if aligned_duration > 0:
        return round((matched_duration / aligned_duration) * 100, 2)
    return 0


def analyze_diagnostic_file(file_path):
    """分析诊断文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取基本信息
        metadata = data.get('metadata', {})
        segments = data.get('segments', [])
        statistics = data.get('statistics', {})
        
        if not segments:
            return None
        
        # RTK信息
        rtk_duration = metadata.get('rtk_duration', 0)
        matched_duration = metadata.get('matched_duration', 0)
        
        # 感知轨迹时间范围
        perception_start = None
        perception_end = None
        
        for seg in segments:
            start_time = parse_timestamp(seg.get('start_time', ''))
            end_time = parse_timestamp(seg.get('end_time', ''))
            
            if start_time and end_time:
                if perception_start is None or start_time < perception_start:
                    perception_start = start_time
                if perception_end is None or end_time > perception_end:
                    perception_end = end_time
        
        if not perception_start or not perception_end:
            return None
        
        # 估算RTK时间范围（基于感知轨迹和RTK时长）
        # 这是一个近似计算，因为诊断文件中没有RTK的具体时间范围
        rtk_start = perception_start  # 假设RTK开始时间
        rtk_end = datetime.fromtimestamp(rtk_start.timestamp() + rtk_duration)
        
        # 计算两种覆盖率
        old_coverage = statistics.get('coverage_rate', 0)
        new_coverage = calculate_new_coverage(
            rtk_start, rtk_end, perception_start, perception_end, matched_duration
        )
        
        return {
            'file': os.path.basename(file_path),
            'rtk_duration': rtk_duration,
            'matched_duration': matched_duration,
            'perception_start': perception_start,
            'perception_end': perception_end,
            'perception_duration': (perception_end - perception_start).total_seconds(),
            'old_coverage': old_coverage,
            'new_coverage': new_coverage,
            'difference': old_coverage - new_coverage,
            'segments_count': len(segments)
        }
        
    except Exception as e:
        print(f"分析文件 {file_path} 失败: {e}")
        return None


def main():
    """主函数"""
    print("=" * 80)
    print("覆盖率计算方法对比分析")
    print("=" * 80)
    
    # 查找所有诊断文件
    diagnostic_files = []
    for root, dirs, files in os.walk('output'):
        for file in files:
            if file.endswith('_diagnostic.json'):
                diagnostic_files.append(os.path.join(root, file))
    
    if not diagnostic_files:
        print("未找到诊断文件")
        return
    
    print(f"找到 {len(diagnostic_files)} 个诊断文件")
    print()
    
    # 分析结果
    results = []
    for file_path in diagnostic_files:
        result = analyze_diagnostic_file(file_path)
        if result:
            results.append(result)
    
    if not results:
        print("没有有效的分析结果")
        return
    
    # 显示结果
    print(f"{'文件名':<40} {'旧覆盖率':<10} {'新覆盖率':<10} {'差异':<10} {'状态'}")
    print("-" * 80)
    
    over_100_count = 0
    fixed_count = 0
    
    for result in results:
        status = ""
        if result['old_coverage'] > 100:
            over_100_count += 1
            if result['new_coverage'] <= 100:
                fixed_count += 1
                status = "✅ 已修正"
            else:
                status = "⚠️ 仍超100%"
        else:
            status = "✓ 正常"
        
        print(f"{result['file']:<40} {result['old_coverage']:<10.2f} {result['new_coverage']:<10.2f} "
              f"{result['difference']:<10.2f} {status}")
    
    print("-" * 80)
    print(f"统计摘要:")
    print(f"  总文件数: {len(results)}")
    print(f"  旧方法超过100%的文件: {over_100_count}")
    print(f"  新方法修正的文件: {fixed_count}")
    print(f"  修正率: {(fixed_count/over_100_count*100) if over_100_count > 0 else 0:.1f}%")
    
    # 显示详细分析
    print("\n" + "=" * 80)
    print("详细分析（仅显示超过100%的情况）")
    print("=" * 80)
    
    for result in results:
        if result['old_coverage'] > 100:
            print(f"\n文件: {result['file']}")
            print(f"  RTK时长: {result['rtk_duration']:.1f}s")
            print(f"  感知轨迹时长: {result['perception_duration']:.1f}s")
            print(f"  匹配时长: {result['matched_duration']:.1f}s")
            print(f"  旧方法覆盖率: {result['old_coverage']:.2f}%")
            print(f"  新方法覆盖率: {result['new_coverage']:.2f}%")
            print(f"  差异: {result['difference']:.2f}%")
            
            if result['matched_duration'] > result['rtk_duration']:
                print(f"  问题原因: 匹配时长({result['matched_duration']:.1f}s) > RTK时长({result['rtk_duration']:.1f}s)")
    
    print("\n" + "=" * 80)
    print("修改效果总结:")
    print("1. 新方法基于时间对齐范围计算，避免了超过100%的不合理情况")
    print("2. 更准确地反映感知系统在整个时间窗口中的覆盖情况")
    print("3. 考虑了RTK和感知轨迹的时间差异，提供更合理的评估指标")
    print("=" * 80)


if __name__ == "__main__":
    main()

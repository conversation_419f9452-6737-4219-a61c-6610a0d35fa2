# 📋 最佳实践指南

## 🎯 数据准备

### 1. 数据格式要求

#### 感知数据 (JSON格式)
```json
{
  "timestamp": "2023-01-01T12:00:00.000Z",  // ISO 8601格式时间戳
  "objects": [
    {
      "id": "obj_001",                       // 唯一目标ID
      "position": {"x": 100.0, "y": 200.0}, // 位置坐标 (米)
      "velocity": {"vx": 10.0, "vy": 0.0},  // 速度向量 (m/s)
      "heading": 90.0                       // 航向角 (度)
    }
  ]
}
```

#### RTK数据 (LOG格式)
```
timestamp,latitude,longitude,altitude,heading,speed
2023-01-01T12:00:00.000Z,39.9042,116.4074,50.0,90.0,36.0
```

### 2. 数据质量检查

#### 时间戳一致性
- ✅ 确保时间戳格式统一 (ISO 8601)
- ✅ 检查时区设置正确性
- ✅ 验证时间序列的连续性

#### 坐标系统
- ✅ 感知数据使用局部坐标系 (米)
- ✅ RTK数据使用WGS84地理坐标系
- ✅ 确保坐标转换参数正确

#### 数据完整性
- ✅ 检查必要字段是否完整
- ✅ 验证数值范围的合理性
- ✅ 处理缺失值和异常值

## ⚙️ 配置优化

### 1. 时间同步配置

```json
{
  "time_sync": {
    "perception_timezone": "Asia/Shanghai",  // 感知数据时区
    "rtk_timezone": "UTC",                   // RTK数据时区
    "time_tolerance": 0.1                    // 时间容差 (秒)
  }
}
```

**建议:**
- 根据实际数据采集环境设置正确时区
- 时间容差设置为数据采集频率的1/10
- 对于高频数据 (>10Hz)，建议时间容差 ≤ 0.05秒

### 2. 空间过滤优化

```json
{
  "corridor": {
    "enabled": true,
    "long_buffer_meters": 25.0,    // 根据车辆速度调整
    "lat_buffer_meters": 5.0,      // 根据道路宽度调整
    "time_buffer_seconds": 5.0     // 根据数据延迟调整
  }
}
```

**建议:**
- 高速场景: 增大纵向缓冲区 (30-50米)
- 城市场景: 减小横向缓冲区 (3-5米)
- 实时场景: 减小时间缓冲区 (1-3秒)

### 3. 匹配算法调优

```json
{
  "matching": {
    "win_sec": 3.0,              // 时间窗口
    "local_match_thr": 0.7,      // 匹配阈值
    "distance_threshold": 5.0     // 距离阈值 (米)
  }
}
```

**调优策略:**
- **高精度场景**: 提高匹配阈值 (0.8-0.9)
- **噪声数据**: 降低匹配阈值 (0.5-0.6)
- **密集交通**: 减小距离阈值 (2-3米)
- **稀疏场景**: 增大时间窗口 (5-10秒)

## 🚀 批量处理优化

### 1. 处理模式选择

#### 串行处理 (batch_simple.py)
**适用场景:**
- 小批量数据 (< 20个任务)
- 调试和测试阶段
- 内存受限环境
- 需要详细错误追踪

```bash
python batch_simple.py \
    --batch data/batch.csv \
    --config config/unified_config.json \
    --output output/serial_results
```

#### 并行处理 (batch_parallel_linux_optimized.py)
**适用场景:**
- 大批量数据 (> 20个任务)
- 生产环境
- 多核CPU系统
- 追求处理效率

```bash
python batch_parallel_linux_optimized.py \
    --batch data/batch.csv \
    --config config/unified_config.json \
    --output output/parallel_results \
    --workers 4
```

### 2. 性能优化建议

#### 进程数配置
```python
# 推荐配置公式
optimal_workers = min(cpu_count(), task_count, 8)

# 内存受限时
memory_limited_workers = available_memory_gb // 2

# 最终选择
workers = min(optimal_workers, memory_limited_workers)
```

#### 批次大小控制
- **小文件** (< 10MB): 50-100个任务/批次
- **中等文件** (10-100MB): 20-50个任务/批次  
- **大文件** (> 100MB): 5-20个任务/批次

### 3. 错误处理策略

#### 任务级错误恢复
```python
# 在批量处理中实现重试机制
max_retries = 3
for attempt in range(max_retries):
    try:
        result = process_task(task)
        break
    except Exception as e:
        if attempt == max_retries - 1:
            log_error(task, e)
        else:
            time.sleep(1)  # 短暂延迟后重试
```

#### 部分失败处理
- ✅ 记录失败任务详细信息
- ✅ 继续处理其他任务
- ✅ 生成部分结果报告
- ✅ 提供失败任务重新处理选项

## 📊 结果分析

### 1. 质量评估指标

#### 匹配质量
- **覆盖率** > 90%: 优秀
- **覆盖率** 80-90%: 良好
- **覆盖率** < 80%: 需要调优

#### 精度指标
- **位置精度** < 2米: 优秀
- **速度精度** < 1m/s: 良好
- **航向精度** < 5度: 可接受

#### 异常检测
- **分裂次数** < 5%: 正常
- **ID切换** < 2%: 可接受
- **漏检率** < 10%: 合理

### 2. 报告解读

#### HTML报告使用
1. **总览分析**: 查看整体成功率和性能指标
2. **任务详情**: 点击链接查看单个任务的详细分析
3. **异常识别**: 关注红色标记的失败任务
4. **趋势分析**: 对比不同任务的指标差异

#### 数据导出
- **CSV格式**: 用于进一步统计分析
- **JSON格式**: 用于程序化处理
- **HTML格式**: 用于报告展示

## 🔧 故障排除

### 1. 常见问题

#### 时间同步失败
**症状**: 匹配率极低，大量时间错误
**解决方案**:
```json
{
  "time_sync": {
    "perception_timezone": "Asia/Shanghai",  // 检查时区设置
    "rtk_timezone": "UTC",
    "time_tolerance": 0.5                    // 临时增大容差
  }
}
```

#### 内存不足
**症状**: 进程被杀死，系统卡顿
**解决方案**:
- 减少并行进程数: `--workers 2`
- 分批处理: 将大批次拆分为小批次
- 增加虚拟内存或升级硬件

#### 匹配精度差
**症状**: 位置误差大，匹配分数低
**解决方案**:
- 检查坐标系转换参数
- 调整空间过滤参数
- 验证数据质量和时间同步

### 2. 调试技巧

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 单任务测试
```bash
# 先用单文件处理验证配置
python main.py \
    --perception data/test.txt \
    --rtk data/test.log \
    --config config/debug_config.json
```

#### 渐进式调优
1. 从默认配置开始
2. 逐步调整单个参数
3. 观察结果变化
4. 记录最佳配置

## 📈 性能监控

### 1. 关键指标

#### 处理性能
- **吞吐量**: 任务数/小时
- **并行效率**: 实际加速比/理论加速比
- **内存使用**: 峰值内存占用
- **CPU利用率**: 平均CPU使用率

#### 质量指标
- **成功率**: 成功任务数/总任务数
- **平均精度**: 所有任务的平均位置精度
- **异常率**: 检测到异常的任务比例

### 2. 监控工具

#### 系统监控
```bash
# Linux系统监控
htop                    # CPU和内存使用
iotop                   # 磁盘I/O监控
nvidia-smi              # GPU使用 (如果适用)
```

#### 应用监控
```python
# 在代码中添加性能监控
import time
import psutil

start_time = time.time()
start_memory = psutil.virtual_memory().used

# 处理逻辑...

end_time = time.time()
end_memory = psutil.virtual_memory().used

print(f"处理时间: {end_time - start_time:.2f}秒")
print(f"内存增长: {(end_memory - start_memory) / 1024**2:.2f}MB")
```

## 🎯 生产部署

### 1. 环境配置

#### 硬件要求
- **CPU**: 8核以上推荐
- **内存**: 16GB以上推荐
- **存储**: SSD推荐，足够的空间存储结果
- **网络**: 如果数据来源于网络存储

#### 软件环境
```bash
# Python环境
python >= 3.8
pip install -r requirements.txt

# 系统依赖 (Linux)
sudo apt-get update
sudo apt-get install python3-dev build-essential
```

### 2. 自动化部署

#### 批处理脚本
```bash
#!/bin/bash
# batch_process.sh

BATCH_FILE="data/daily_batch.csv"
CONFIG_FILE="config/production_config.json"
OUTPUT_DIR="output/$(date +%Y%m%d)"

python batch_parallel_linux_optimized.py \
    --batch "$BATCH_FILE" \
    --config "$CONFIG_FILE" \
    --output "$OUTPUT_DIR" \
    --workers 6

# 发送结果通知
if [ $? -eq 0 ]; then
    echo "批量处理完成: $OUTPUT_DIR" | mail -s "处理成功" <EMAIL>
else
    echo "批量处理失败" | mail -s "处理失败" <EMAIL>
fi
```

#### 定时任务
```bash
# 添加到crontab
0 2 * * * /path/to/batch_process.sh >> /var/log/batch_process.log 2>&1
```

### 3. 监控和维护

#### 日志管理
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日志轮转
handler = RotatingFileHandler(
    'logs/batch_process.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
logging.getLogger().addHandler(handler)
```

#### 结果备份
```bash
# 定期备份重要结果
rsync -av output/ backup_server:/backup/trajectory_analysis/
```

---

**💡 提示**: 这些最佳实践基于实际项目经验总结，建议根据具体应用场景进行调整和优化。

# 🚗 车路协同轨迹匹配与批量分析系统

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

一个专业的车路协同感知数据与RTK轨迹数据匹配分析系统，支持单文件处理和大规模批量分析，提供详细的精度评估和可视化报告。

## 🌟 核心特性

### 📊 数据处理能力
- **多格式支持**: JSON、LOG、CSV等多种数据格式
- **智能预处理**: 自动数据格式检测和标准化
- **时间同步**: UTC/北京时间自动转换和对齐
- **空间过滤**: ROI区域和走廊过滤算法

### 🎯 轨迹匹配算法
- **距离匹配**: 基于欧几里得距离的轨迹匹配
- **时间窗口**: 可配置的时间容差匹配
- **评分系统**: 多维度匹配质量评估
- **异常检测**: 轨迹分裂、ID切换、漏检检测

### 📈 精度分析
- **位置精度**: 平均/最大/标准差位置误差
- **速度精度**: 绝对/相对速度误差分析
- **航向精度**: 航向角度误差统计
- **覆盖率分析**: 时间和空间覆盖率评估

### 🚀 批量处理
- **串行处理**: 稳定可靠的顺序处理模式
- **并行处理**: 多进程并行处理，支持Linux优化
- **进度监控**: 实时处理进度和状态反馈
- **错误恢复**: 智能错误处理和任务恢复

### 📊 可视化报告
- **HTML报告**: 专业的交互式分析报告
- **批量汇总**: 多任务综合分析和对比
- **图表展示**: Chart.js驱动的动态图表
- **智能链接**: 报告间的智能导航系统

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows/Linux系统
- 8GB+ 内存推荐

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd trajectory-matching-system
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **验证安装**
```bash
python main.py --help
```

### 基础使用

#### 单文件处理
```bash
# 处理单对感知和RTK文件
python main.py \
    --perception data/save_1753355915725.txt \
    --rtk data/31.log \
    --output output/single_analysis
```

#### 批量处理
```bash
# 串行批量处理
python batch_simple.py \
    --batch data/batch.csv \
    --config config/unified_config.json \
    --output output/batch_results

# 并行批量处理（推荐）
python batch_parallel_linux_optimized.py \
    --batch data/batch.csv \
    --config config/unified_config.json \
    --output output/parallel_results \
    --workers 4
```

## 📁 项目结构

```
trajectory-matching-system/
├── 📄 README.md                    # 项目主文档
├── 📄 requirements.txt             # Python依赖
├── 📄 main.py                      # 单文件处理入口
├── 📄 batch_simple.py              # 串行批量处理
├── 📄 batch_main.py                # 面向对象批量处理
├── 📄 batch_parallel_linux_optimized.py  # 并行批量处理
├── 📄 demo_enhanced_html_reports.py      # 功能演示脚本
├── 📁 config/                      # 配置文件目录
│   ├── unified_config.json         # 主配置文件
│   └── default.json                # 默认配置
├── 📁 core/                        # 核心算法模块
│   ├── trajectory_matcher.py       # 轨迹匹配器
│   ├── batch_report_generator.py   # 批量报告生成器
│   ├── preprocessor.py             # 数据预处理器
│   ├── corridor_filter.py          # 走廊过滤器
│   └── ...                         # 其他核心模块
├── 📁 templates/                   # HTML模板
│   ├── accuracy_report_template.html     # 精度分析模板
│   └── batch_summary_template.html       # 批量汇总模板
├── 📁 data/                        # 示例数据
│   ├── batch.csv                   # 批量任务配置
│   ├── save_1753355915725.txt      # 示例感知数据
│   └── *.log                       # 示例RTK数据
├── 📁 docs/                        # 技术文档目录
│   ├── INDEX.md                    # 技术文档索引
│   ├── API_REFERENCE.md            # API参考文档
│   ├── BEST_PRACTICES.md           # 最佳实践指南
│   ├── config_reference.md         # 配置参考手册
│   └── ...                         # 其他技术文档
├── 📁 tests/                       # 测试用例
└── 📁 output/                      # 输出结果目录
```

## 📊 输出结果

### 单文件处理输出
- `trajectory_matched.csv` - 匹配后的轨迹数据
- `diagnostic.json` - 详细的诊断信息和统计
- `reports/accuracy_report.html` - 交互式精度分析报告

### 批量处理输出
- `batch_summary_report.html` - **增强版批量汇总报告**
- `batch_summary.json` - JSON格式汇总数据
- `batch_summary.csv` - CSV格式汇总数据
- 各任务子目录包含完整的单文件处理结果

## 🎯 增强HTML报告功能

### 批量汇总报告特性
- **📊 详细任务指标**: 匹配点数、覆盖率、平均匹配分数
- **🎯 精度分析指标**: 位置精度、速度精度、航向精度
- **⚠️ 异常检测统计**: 分裂次数、ID切换、漏检统计
- **🔗 智能链接系统**: 精度报告和JSON数据文件链接
- **🎨 响应式设计**: 支持水平滚动的12列详细表格

### 报告导航
- 点击 **📊 精度报告** 查看详细HTML分析报告
- 点击 **📄 JSON数据** 查看原始诊断数据文件
- 表格支持水平滚动查看所有指标列
- 状态颜色标识：绿色=成功，红色=失败

## 📖 详细文档

### 🎯 快速导航
- [📚 **文档导航中心**](DOCUMENTATION.md) - **一站式文档导航，快速找到所需资源**
- [📖 **文档使用指南**](文档使用指南.md) - **如何高效使用项目文档**
- [🚀 **快速开始指南**](QUICK_START.md) - **5分钟快速上手**
- [📊 **批量处理指南**](BATCH_PROCESSING_GUIDE.md) - **详细的批量处理说明**

### 📋 主要文档
- [📊 批量处理HTML报告使用指南](批量处理HTML报告使用指南.md) - 增强版HTML报告功能
- [🏆 项目完成总结](项目完成总结.md) - 完整的项目成果总结
- [🧹 项目清理与文档完善完成总结](项目清理与文档完善完成总结.md) - 项目清理记录

### 🔧 技术文档
- [📚 docs/INDEX.md](docs/INDEX.md) - 技术文档索引
- [📚 docs/API_REFERENCE.md](docs/API_REFERENCE.md) - API参考文档
- [📋 docs/BEST_PRACTICES.md](docs/BEST_PRACTICES.md) - 最佳实践指南
- [⚙️ docs/config_reference.md](docs/config_reference.md) - 配置参考手册

## 🎮 功能演示

运行演示脚本体验完整功能：

```bash
# 运行增强HTML报告演示
python demo_enhanced_html_reports.py
```

演示包含：
- 串行和并行批量处理对比
- 增强HTML报告功能展示
- 自动浏览器打开和报告查看
- 详细功能说明和使用提示

## 🔧 配置说明

主配置文件 `config/unified_config.json` 包含：

- **time_sync**: 时间同步设置
- **spatial_filter**: 空间过滤参数  
- **matching**: 轨迹匹配算法参数
- **anomaly_detection**: 异常检测设置
- **accuracy_analysis**: 精度分析配置

## 📈 性能特性

- **并行处理效率**: 4进程可达2.3x加速比
- **内存优化**: 支持大批量数据处理
- **跨平台兼容**: Windows/Linux完美支持
- **错误恢复**: 智能错误处理和任务恢复

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**🎯 项目状态**: 生产就绪 | **📊 最新版本**: v2.0 增强版 | **🔄 最后更新**: 2025-07-31

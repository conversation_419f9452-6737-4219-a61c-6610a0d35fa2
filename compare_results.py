#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三种配置模式结果对比脚本
"""

import json
import os

def compare_results():
    modes = ['simple_distance', 'performance_optimized', 'compatibility_mode']
    results = {}

    for mode in modes:
        json_file = f'output/{mode}/rtk_part005_AJ06993PAJ00115B1_diagnostic.json'
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                results[mode] = {
                    'match_score': data['statistics'].get('average_match_score', 0.0),
                    'matched_pairs': data['statistics'].get('matched_pairs', 0),
                    'coverage_rate': data['statistics']['coverage_rate'] / 100.0,  # 转换为小数
                    'processing_time': data.get('processing_time_seconds', 'N/A'),
                    'rtk_duration': data['metadata']['rtk_duration'],
                    'matched_duration': data['metadata']['matched_duration']
                }

    print('=' * 80)
    print('三种配置模式对比结果')
    print('=' * 80)
    print(f"{'模式':<20} {'匹配分数':<12} {'匹配点数':<10} {'覆盖率':<10} {'处理时间(秒)':<12}")
    print('-' * 80)

    mode_names = {
        'simple_distance': '简单距离模式',
        'performance_optimized': '性能优化模式', 
        'compatibility_mode': '兼容模式'
    }

    for mode, data in results.items():
        mode_name = mode_names[mode]
        match_score = data['match_score']
        matched_pairs = data['matched_pairs']
        coverage_rate = data['coverage_rate']
        processing_time = data['processing_time']
        
        print(f"{mode_name:<18} {match_score:<12.3f} {matched_pairs:<10} {coverage_rate:<10.1%} {processing_time:<12}")

    print('\n' + '=' * 80)
    print('详细分析:')
    print('=' * 80)
    
    # 找出最佳匹配分数
    best_score_mode = max(results.keys(), key=lambda x: results[x]['match_score'])
    best_score = results[best_score_mode]['match_score']
    
    print(f"🏆 最高匹配分数: {mode_names[best_score_mode]} ({best_score:.3f})")
    
    # 分析差异
    scores = [results[mode]['match_score'] for mode in modes if mode in results]
    if len(scores) > 1:
        score_diff = max(scores) - min(scores)
        print(f"📊 匹配分数差异: {score_diff:.3f}")
        
        if score_diff > 0.1:
            print("⚠️  不同模式间存在显著差异，建议根据具体需求选择")
        else:
            print("✅ 不同模式间差异较小，可根据性能需求选择")

if __name__ == "__main__":
    compare_results()

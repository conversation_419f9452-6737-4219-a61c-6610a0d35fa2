#!/usr/bin/env python3
"""
测试覆盖率计算逻辑的脚本
验证新的时间对齐覆盖率计算方法
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.output_generator import OutputGenerator
from core.data_utils import RTKPoint
from core.simple_distance_matcher import TrajectorySegment
from core.config_loader import load_config


class MockPerceptionPoint:
    """模拟感知点"""
    def __init__(self, timestamp, lat, lon, id_val):
        self.timestamp = timestamp
        self.lat = lat
        self.lon = lon
        self.id = id_val


def create_test_data():
    """创建测试数据"""
    base_time = datetime(2025, 7, 31, 10, 0, 0)
    
    # 创建RTK轨迹点 (10秒到30秒)
    rtk_points = []
    for i in range(20):  # 20秒的RTK数据
        rtk_points.append(RTKPoint(
            timestamp=base_time + timedelta(seconds=10 + i),
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            speed=10.0,
            heading=90.0
        ))
    
    # 创建感知轨迹段 (5秒到35秒，超出RTK范围)
    perception_points = []
    for i in range(30):  # 30秒的感知数据
        perception_points.append(MockPerceptionPoint(
            timestamp=base_time + timedelta(seconds=5 + i),
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            id_val="test_id_001"
        ))
    
    # 创建轨迹段
    matched_chain = [TrajectorySegment(
        id="test_id_001",
        points=perception_points,
        start_time=base_time + timedelta(seconds=5),
        end_time=base_time + timedelta(seconds=35)
    )]
    
    return rtk_points, matched_chain


def test_old_vs_new_calculation():
    """测试旧版本vs新版本的覆盖率计算"""
    print("=" * 60)
    print("覆盖率计算方法对比测试")
    print("=" * 60)
    
    rtk_points, matched_chain = create_test_data()
    
    # 打印时间范围信息
    rtk_start = rtk_points[0].timestamp
    rtk_end = rtk_points[-1].timestamp
    perception_start = matched_chain[0].start_time
    perception_end = matched_chain[0].end_time
    
    print(f"RTK轨迹时间范围: {rtk_start} ~ {rtk_end}")
    print(f"RTK轨迹时长: {(rtk_end - rtk_start).total_seconds():.1f}s")
    print(f"感知轨迹时间范围: {perception_start} ~ {perception_end}")
    print(f"感知轨迹时长: {matched_chain[0].duration:.1f}s")
    print()
    
    # 旧版本计算方法
    rtk_duration_old = (rtk_end - rtk_start).total_seconds()
    matched_duration = matched_chain[0].duration
    coverage_rate_old = (matched_duration / rtk_duration_old * 100) if rtk_duration_old > 0 else 0
    
    print("旧版本计算方法:")
    print(f"  分母 (RTK时长): {rtk_duration_old:.1f}s")
    print(f"  分子 (匹配时长): {matched_duration:.1f}s")
    print(f"  覆盖率: {coverage_rate_old:.2f}%")
    print()
    
    # 新版本计算方法
    aligned_start = min(rtk_start, perception_start)
    aligned_end = max(rtk_end, perception_end)
    aligned_duration = (aligned_end - aligned_start).total_seconds()
    coverage_rate_new = (matched_duration / aligned_duration * 100) if aligned_duration > 0 else 0
    
    print("新版本计算方法 (时间对齐):")
    print(f"  对齐开始时间: {aligned_start}")
    print(f"  对齐结束时间: {aligned_end}")
    print(f"  分母 (对齐时长): {aligned_duration:.1f}s")
    print(f"  分子 (匹配时长): {matched_duration:.1f}s")
    print(f"  覆盖率: {coverage_rate_new:.2f}%")
    print()
    
    # 使用OutputGenerator验证
    config = load_config('config/unified_config.json')
    output_generator = OutputGenerator(config)
    coverage_rate_generator = output_generator._calculate_coverage_rate(matched_chain, rtk_points)
    
    print("OutputGenerator计算结果:")
    print(f"  覆盖率: {coverage_rate_generator:.2f}%")
    print()
    
    # 分析差异
    print("分析:")
    print(f"  旧方法 vs 新方法差异: {coverage_rate_old - coverage_rate_new:.2f}%")
    if coverage_rate_old > 100:
        print(f"  旧方法超过100%的原因: 感知轨迹时长({matched_duration:.1f}s) > RTK轨迹时长({rtk_duration_old:.1f}s)")
    print(f"  新方法合理性: 覆盖率不会超过100%，反映真实的时间覆盖情况")


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("边界情况测试")
    print("=" * 60)
    
    config = load_config('config/unified_config.json')
    output_generator = OutputGenerator(config)
    
    # 测试1: 空轨迹链
    print("测试1: 空轨迹链")
    rtk_points, _ = create_test_data()
    coverage_rate = output_generator._calculate_coverage_rate([], rtk_points)
    print(f"  结果: {coverage_rate:.2f}% (期望: 0%)")
    
    # 测试2: 空RTK点
    print("\n测试2: 空RTK点")
    _, matched_chain = create_test_data()
    coverage_rate = output_generator._calculate_coverage_rate(matched_chain, [])
    print(f"  结果: {coverage_rate:.2f}% (期望: 0%)")
    
    # 测试3: 完全重叠的时间范围
    print("\n测试3: 完全重叠的时间范围")
    base_time = datetime(2025, 7, 31, 10, 0, 0)
    
    # RTK和感知轨迹完全重叠
    rtk_points_overlap = []
    perception_points_overlap = []
    for i in range(10):
        time_point = base_time + timedelta(seconds=i)
        rtk_points_overlap.append(RTKPoint(
            timestamp=time_point,
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            speed=10.0,
            heading=90.0
        ))
        perception_points_overlap.append(MockPerceptionPoint(
            timestamp=time_point,
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            id_val="overlap_id"
        ))
    
    matched_chain_overlap = [TrajectorySegment(
        id="overlap_id",
        points=perception_points_overlap,
        start_time=base_time,
        end_time=base_time + timedelta(seconds=9)
    )]
    
    coverage_rate = output_generator._calculate_coverage_rate(matched_chain_overlap, rtk_points_overlap)
    print(f"  结果: {coverage_rate:.2f}% (期望: 100%)")


if __name__ == "__main__":
    test_old_vs_new_calculation()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("新的覆盖率计算方法特点:")
    print("1. 基于时间对齐的范围计算，不会超过100%")
    print("2. 反映感知轨迹在整个时间窗口中的真实覆盖情况")
    print("3. 考虑了RTK和感知轨迹的时间差异")
    print("4. 更准确地反映系统性能")

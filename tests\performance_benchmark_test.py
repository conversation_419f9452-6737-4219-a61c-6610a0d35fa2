#!/usr/bin/env python3
"""
性能基准测试
端到端性能测试，验证优化效果是否达到预期的5-7s目标
"""

import sys
import os
import json
import time
import statistics
from datetime import datetime, timedelta
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.simple_distance_matcher import SimpleDistanceMatcher
from core.data_utils import RTKPoint, PerceptionPoint
from core.config_loader import Config


class PerformanceBenchmarkTest:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = {
            'benchmark_runs': [],
            'summary': {},
            'timestamp': datetime.now().isoformat(),
            'target_performance': {'min_time': 5.0, 'max_time': 7.0}  # 目标性能范围
        }
    
    def create_test_config(self, profile="performance_optimized"):
        """创建测试配置"""
        if profile == "performance_optimized":
            return Config({
                'performance': {
                    'enable_time_aligned_search': True,
                    'rtk_max_time_diff': 3.0,
                    'enable_performance_logging': True
                },
                'scoring': {
                    'method': 'unified',
                    'enable_score_cache': True,
                    'pre_filter_threshold': 0.4,
                    'cache_debug_output': False
                },
                'matching': {
                    'local_match_thr': 0.7,
                    'min_segment_length': 2
                },
                'corridor': {
                    'enable_corridor_filter': False  # 简化测试
                }
            })
        else:  # compatibility_mode
            return Config({
                'performance': {
                    'enable_time_aligned_search': False,
                    'rtk_max_time_diff': 3.0,
                    'enable_performance_logging': True
                },
                'scoring': {
                    'method': 'unified',
                    'enable_score_cache': False,
                    'pre_filter_threshold': 0.0,
                    'cache_debug_output': False
                },
                'matching': {
                    'local_match_thr': 0.7,
                    'min_segment_length': 2
                },
                'corridor': {
                    'enable_corridor_filter': False
                }
            })
    
    def generate_large_dataset(self, rtk_count=2000, perception_count=1000, object_count=10):
        """生成大规模测试数据集（模拟真实场景）"""
        print(f"生成测试数据: RTK点={rtk_count}, 感知点={perception_count}, 目标数={object_count}")
        
        base_time = datetime.now()
        
        # 生成RTK轨迹（模拟20秒，100Hz采样）
        rtk_points = []
        for i in range(rtk_count):
            rtk_point = RTKPoint(
                timestamp=base_time + timedelta(seconds=i * 0.01),  # 100Hz
                lat=39.9 + i * 0.00005,  # 更密集的轨迹
                lon=116.3 + i * 0.00005,
                alt=50.0,
                accuracy=0.1
            )
            rtk_points.append(rtk_point)
        
        # 生成多目标感知轨迹
        perception_points = []
        import random
        
        for obj_id in range(1, object_count + 1):
            points_per_object = perception_count // object_count
            
            for i in range(points_per_object):
                # 模拟不同的轨迹模式
                time_offset = random.uniform(0, 15)  # 随机时间偏移
                spatial_offset = obj_id * 0.0001  # 空间偏移
                
                per_point = PerceptionPoint(
                    timestamp=base_time + timedelta(seconds=time_offset + i * 0.02),  # 50Hz
                    id=obj_id,
                    lat=39.9 + i * 0.0001 + spatial_offset + random.uniform(-0.00002, 0.00002),
                    lon=116.3 + i * 0.0001 + spatial_offset + random.uniform(-0.00002, 0.00002),
                    speed=random.uniform(8, 12),
                    heading=random.uniform(85, 95)
                )
                perception_points.append(per_point)
        
        return rtk_points, perception_points
    
    def run_benchmark(self, config, test_name, rtk_points, perception_points, runs=3):
        """运行性能基准测试"""
        print(f"\n运行基准测试: {test_name}")
        print(f"  数据规模: RTK={len(rtk_points)}, 感知={len(perception_points)}")
        
        execution_times = []
        detailed_results = []
        
        for run in range(runs):
            print(f"  第{run+1}次运行...")
            
            # 创建匹配器
            matcher = SimpleDistanceMatcher(config, rtk_points)
            
            # 记录各阶段时间
            stage_times = {}
            
            # 阶段1: 空间过滤
            start_time = time.perf_counter()
            filtered_perception = matcher.filter_perception_points(perception_points)
            stage_times['spatial_filtering'] = time.perf_counter() - start_time
            
            # 阶段2: 轨迹段构建
            start_time = time.perf_counter()
            segments = matcher.build_segments(filtered_perception)
            stage_times['segment_building'] = time.perf_counter() - start_time
            
            # 阶段3: 核心链构建（主要优化目标）
            start_time = time.perf_counter()
            core_chain = matcher.build_core_chain(segments)
            stage_times['core_chain_building'] = time.perf_counter() - start_time
            
            # 总执行时间
            total_time = sum(stage_times.values())
            execution_times.append(total_time)
            
            # 收集详细结果
            result = {
                'run': run + 1,
                'total_time': total_time,
                'stage_times': stage_times,
                'filtered_points': len(filtered_perception),
                'segments_count': len(segments),
                'core_chain_count': len(core_chain),
                'performance_stats': matcher.performance_monitor.get_stats() if hasattr(matcher, 'performance_monitor') else {},
                'cache_stats': matcher.score_cache.get_stats() if hasattr(matcher, 'score_cache') else {}
            }
            detailed_results.append(result)
            
            print(f"    总时间: {total_time:.3f}s")
            print(f"    核心链构建: {stage_times['core_chain_building']:.3f}s")
        
        # 计算统计信息
        avg_time = statistics.mean(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        std_dev = statistics.stdev(execution_times) if len(execution_times) > 1 else 0
        
        benchmark_result = {
            'test_name': test_name,
            'runs': runs,
            'execution_times': execution_times,
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'std_dev': std_dev,
            'detailed_results': detailed_results,
            'meets_target': self.check_performance_target(avg_time)
        }
        
        print(f"  平均时间: {avg_time:.3f}s ± {std_dev:.3f}s")
        print(f"  时间范围: {min_time:.3f}s - {max_time:.3f}s")
        print(f"  目标达成: {'✓' if benchmark_result['meets_target'] else '✗'}")
        
        return benchmark_result
    
    def check_performance_target(self, avg_time):
        """检查是否达到性能目标"""
        target = self.results['target_performance']
        return target['min_time'] <= avg_time <= target['max_time']
    
    def run_comprehensive_benchmark(self):
        """运行综合性能基准测试"""
        print("开始综合性能基准测试...")
        print(f"目标性能: {self.results['target_performance']['min_time']}-{self.results['target_performance']['max_time']}s")
        
        # 生成测试数据
        rtk_points, perception_points = self.generate_large_dataset()
        
        # 测试优化版本
        print(f"\n{'='*60}")
        print("测试优化版本 (performance_optimized)")
        print(f"{'='*60}")
        
        optimized_config = self.create_test_config("performance_optimized")
        optimized_result = self.run_benchmark(
            optimized_config, "performance_optimized", 
            rtk_points, perception_points, runs=5
        )
        self.results['benchmark_runs'].append(optimized_result)
        
        # 测试传统版本（作为对比）
        print(f"\n{'='*60}")
        print("测试传统版本 (compatibility_mode)")
        print(f"{'='*60}")
        
        legacy_config = self.create_test_config("compatibility_mode")
        legacy_result = self.run_benchmark(
            legacy_config, "compatibility_mode", 
            rtk_points, perception_points, runs=3
        )
        self.results['benchmark_runs'].append(legacy_result)
        
        # 计算性能提升
        performance_improvement = legacy_result['avg_time'] / optimized_result['avg_time']
        
        print(f"\n{'='*60}")
        print("性能对比")
        print(f"{'='*60}")
        print(f"优化版本: {optimized_result['avg_time']:.3f}s")
        print(f"传统版本: {legacy_result['avg_time']:.3f}s")
        print(f"性能提升: {performance_improvement:.1f}×")
        print(f"目标达成: {'✓' if optimized_result['meets_target'] else '✗'}")
    
    def generate_summary(self):
        """生成测试总结"""
        if not self.results['benchmark_runs']:
            return
        
        optimized_run = next((r for r in self.results['benchmark_runs'] if r['test_name'] == 'performance_optimized'), None)
        legacy_run = next((r for r in self.results['benchmark_runs'] if r['test_name'] == 'compatibility_mode'), None)
        
        summary = {
            'target_achieved': optimized_run['meets_target'] if optimized_run else False,
            'optimized_avg_time': optimized_run['avg_time'] if optimized_run else None,
            'legacy_avg_time': legacy_run['avg_time'] if legacy_run else None,
            'performance_improvement': legacy_run['avg_time'] / optimized_run['avg_time'] if optimized_run and legacy_run else None,
            'optimization_effective': True if optimized_run and legacy_run and optimized_run['avg_time'] < legacy_run['avg_time'] else False
        }
        
        self.results['summary'] = summary
        
        print(f"\n{'='*60}")
        print("基准测试总结")
        print(f"{'='*60}")
        print(f"目标性能达成: {'是' if summary['target_achieved'] else '否'}")
        if summary['optimized_avg_time']:
            print(f"优化版本平均时间: {summary['optimized_avg_time']:.3f}s")
        if summary['legacy_avg_time']:
            print(f"传统版本平均时间: {summary['legacy_avg_time']:.3f}s")
        if summary['performance_improvement']:
            print(f"性能提升倍数: {summary['performance_improvement']:.1f}×")
        print(f"优化有效性: {'是' if summary['optimization_effective'] else '否'}")
    
    def save_results(self, output_path="tests/performance_benchmark_results.json"):
        """保存测试结果"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n基准测试结果已保存到: {output_path}")


def main():
    """主函数"""
    print("开始性能基准测试...")
    
    tester = PerformanceBenchmarkTest()
    tester.run_comprehensive_benchmark()
    tester.generate_summary()
    tester.save_results()
    
    print("\n性能基准测试完成!")


if __name__ == '__main__':
    main()

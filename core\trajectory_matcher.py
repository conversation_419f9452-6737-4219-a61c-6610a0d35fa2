#!/usr/bin/env python3
"""
车路协同感知轨迹匹配工具
基于简单距离的RTK轨迹与感知数据匹配
支持原始数据格式自动预处理
"""

import argparse
import json
import os
import sys
from datetime import datetime
from pathlib import Path

from .data_utils import DataLoader, Config
from .config_loader import load_config, LegacyConfig
from .simple_distance_matcher import SimpleDistanceMatcher
from .output_generator import OutputGenerator, trim_rtk_by_match_time
from .preprocessor import RawDataPreprocessor

def main():
    parser = argparse.ArgumentParser(description='车路协同感知轨迹匹配工具')
    parser.add_argument('--rtk', required=True, help='RTK数据文件路径 (支持NMEA/.txt或CSV格式)')
    parser.add_argument('--perception', required=True, help='感知数据文件路径 (支持JSON/.txt或CSV格式)')
    parser.add_argument('--config', default='config/unified_config.json', help='配置文件路径')
    parser.add_argument('--profile', default=None,
                       choices=['simple_distance', 'performance_optimized', 'compatibility_mode'],
                       help='配置文件 (simple_distance: 简单距离评分, performance_optimized: 性能优化模式, compatibility_mode: 兼容模式)')
    parser.add_argument('--output-dir', default='output', help='输出目录')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--skip-preprocess', action='store_true', help='跳过预处理，直接使用CSV文件')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.rtk):
        print(f"错误：RTK文件不存在: {args.rtk}")
        sys.exit(1)
    
    if not os.path.exists(args.perception):
        print(f"错误：感知数据文件不存在: {args.perception}")
        sys.exit(1)
    
    # 创建输出目录（提前创建，用于保存预处理文件）
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 预处理原始数据
    rtk_csv_path = args.rtk
    perception_csv_path = args.perception

    if not args.skip_preprocess:
        preprocessor = RawDataPreprocessor()

        # 检测文件格式并进行预处理
        rtk_format = preprocessor.detect_file_format(args.rtk)
        perception_format = preprocessor.detect_file_format(args.perception)

        if args.verbose:
            print(f"RTK文件格式: {rtk_format}")
            print(f"感知文件格式: {perception_format}")

        # 生成预处理文件名
        rtk_name = Path(args.rtk).stem
        per_name = Path(args.perception).stem

        try:
            if rtk_format != 'csv':
                if args.verbose:
                    print(f"预处理RTK文件: {args.rtk}")
                rtk_csv_path = preprocessor.preprocess_rtk_file(
                    args.rtk,
                    str(output_dir / f"{rtk_name}_preprocessed.csv")
                )
                if args.verbose:
                    print(f"预处理后RTK文件: {rtk_csv_path}")

            if perception_format != 'csv':
                if args.verbose:
                    print(f"预处理感知文件: {args.perception}")
                perception_csv_path = preprocessor.preprocess_perception_file(
                    args.perception,
                    str(output_dir / f"{per_name}_preprocessed.csv")
                )
                if args.verbose:
                    print(f"预处理后感知文件: {perception_csv_path}")

        except Exception as e:
            print(f"❌ 预处理失败: {e}")
            sys.exit(1)
    
    # 加载配置
    try:
        unified_config = load_config(args.config, args.profile)
        config = LegacyConfig(unified_config)  # 向后兼容
        
        if args.verbose:
            profile_info = f" (profile: {args.profile})" if args.profile else ""
            print(f"加载配置文件: {args.config}{profile_info}")
            print(f"使用简单距离评分")
            
    except Exception as e:
        print(f"警告：配置加载失败 {e}，使用默认配置")
        config = Config()
    
    # 生成输出文件名
    rtk_name = Path(args.rtk).stem
    per_name = Path(args.perception).stem
    output_prefix = f"{rtk_name}_{per_name}"
    
    matched_csv_path = output_dir / f"{output_prefix}_trajectory_matched.csv"
    diagnostic_json_path = output_dir / f"{output_prefix}_diagnostic.json"
    
    print("=" * 60)
    print("车路协同感知轨迹匹配工具")
    print("=" * 60)
    print(f"RTK文件: {args.rtk}")
    print(f"感知文件: {args.perception}")
    print(f"配置文件: {args.config}")
    print(f"输出目录: {args.output_dir}")
    print(f"详细输出: {args.verbose}")
    if not args.skip_preprocess:
        print(f"预处理: 启用")
    print()
    
    try:
        # 1. 数据加载
        print("1. 数据加载...")
        data_loader = DataLoader(config)
        
        rtk_points = data_loader.load_rtk_csv(rtk_csv_path)
        perception_points = data_loader.load_perception_csv(perception_csv_path)
        
        print(f"   RTK轨迹点: {len(rtk_points)}")
        print(f"   感知数据点: {len(perception_points)}")
        
        if not rtk_points or not perception_points:
            print("错误：数据为空")
            sys.exit(1)
        
        # 2. 时间同步
        print("2. 时间同步...")
        rtk_points = data_loader.sync_rtk_time(rtk_points)
        perception_points = data_loader.sync_perception_time(perception_points)
        
        rtk_start = rtk_points[0].timestamp
        rtk_end = rtk_points[-1].timestamp
        per_start = perception_points[0].timestamp
        per_end = perception_points[-1].timestamp
        
        print(f"   RTK时间范围: {rtk_start} ~ {rtk_end}")
        print(f"   感知时间范围: {per_start} ~ {per_end}")
        
        # 3. 空间过滤 (走廊过滤或ROI过滤)
        print("3. 空间过滤...")
        matcher = SimpleDistanceMatcher(config, rtk_points)
        filtered_perception = matcher.filter_perception_points(perception_points)
        
        print(f"   ROI过滤后: {len(filtered_perception)} 点")
        
        # 4. 轨迹段构建
        print("4. 轨迹段构建...")
        segments = matcher.build_segments(filtered_perception)
        
        print(f"   轨迹段数量: {len(segments)}")
        if args.verbose:
            for i, seg in enumerate(segments):
                print(f"      段{i+1}: ID={seg.id}, 时长={seg.duration:.1f}s, 点数={len(seg.points)}")
        
        # 5. 核心链构建
        print("5. 核心链构建...")
        core_chain = matcher.build_core_chain(segments)
        
        print(f"   核心链段数: {len(core_chain)}")
        if args.verbose:
            for i, seg in enumerate(core_chain):
                print(f"      核心段{i+1}: ID={seg.id}, 时长={seg.duration:.1f}s, 分数={getattr(seg, 'final_score', 0):.3f}")
        
        # 6. 异常检测和汇总
        print("6. 异常检测和汇总...")
        # 使用核心链进行异常分析（移除Gap Filling）
        anomalies = matcher.final_anomaly_summary(core_chain, rtk_start, rtk_end)
        
        print(f"   分裂事件: {len(anomalies['split_events'])}")
        print(f"   ID切换: {len(anomalies['id_switches'])}")
        print(f"   漏检间隙: {len(anomalies['missing_gaps'])}")
        print(f"   拒绝段: {len(anomalies['rejected_segments'])}")
        
        if args.verbose:
            for event in anomalies['split_events']:
                score_str = f" 分数={event['score']:.3f}" if 'score' in event else ""
                print(f"      分裂: {event['timestamp']} IDs={event['ids']}{score_str}")
            for event in anomalies['id_switches']:
                print(f"      切换: {event['timestamp']} {event['from_id']}→{event['to_id']} 间隔={event['gap_duration']:.1f}s")
            for gap in anomalies['missing_gaps']:
                print(f"      漏检: {gap['start_time']} ~ {gap['end_time']} 时长={gap['duration']:.1f}s 类型={gap['type']}")
        
        # 7. RTK轨迹剪裁
        print("7. RTK轨迹剪裁...")
        trimmed_rtk = trim_rtk_by_match_time(rtk_points, core_chain, config.rtk_buffer)
        
        print(f"   剪裁后RTK点数: {len(trimmed_rtk)}")
        
        # 8. 输出生成
        print("8. 输出生成...")
        output_generator = OutputGenerator(config)
        
        # 获取完整的分析结果（如果可用）
        analysis_results = getattr(matcher, 'analysis_results', None)

        # 生成匹配CSV
        output_generator.generate_matched_csv(
            trimmed_rtk, core_chain, str(matched_csv_path), anomalies, analysis_results
        )
        print(f"   匹配CSV: {matched_csv_path}")

        # 生成诊断JSON
        output_generator.generate_diagnostic_json(
            anomalies, str(diagnostic_json_path), core_chain, trimmed_rtk, analysis_results
        )
        print(f"   诊断JSON: {diagnostic_json_path}")

        # 生成精度分析HTML报告（如果有精度分析结果）
        if analysis_results and 'accuracy' in analysis_results:
            accuracy_result = analysis_results['accuracy']
            if accuracy_result.success:
                from .report_generator import AccuracyReportGenerator

                # 构建HTML报告路径
                rtk_name = Path(args.rtk).stem
                perception_name = Path(args.perception).stem
                html_report_path = output_dir / "reports" / f"{rtk_name}_{perception_name}_accuracy_analysis_report.html"

                # 创建报告生成器并生成HTML报告
                report_generator = AccuracyReportGenerator()
                success = report_generator.generate_html_report(accuracy_result, str(html_report_path))

                if success:
                    print(f"   精度分析HTML报告: {html_report_path}")
                else:
                    print(f"   ⚠️ 精度分析HTML报告生成失败")
        
        # 10. 统计摘要
        print("\n" + "=" * 60)
        print("匹配结果摘要")
        print("=" * 60)
        
        # 计算统计指标 - 使用时间对齐的覆盖率计算
        if core_chain:
            # 感知轨迹时间范围
            perception_start = min(seg.start_time for seg in core_chain)
            perception_end = max(seg.end_time for seg in core_chain)

            # 计算对齐的时间范围（取两者的并集）
            aligned_start = min(rtk_start, perception_start)
            aligned_end = max(rtk_end, perception_end)
            aligned_duration = (aligned_end - aligned_start).total_seconds()

            # 计算匹配轨迹的总时长
            matched_duration = sum(seg.duration for seg in core_chain)
            coverage_rate = (matched_duration / aligned_duration * 100) if aligned_duration > 0 else 0
        else:
            matched_duration = 0
            coverage_rate = 0
            aligned_duration = (rtk_end - rtk_start).total_seconds()
        
        avg_score = sum(getattr(seg, 'final_score', 0) for seg in core_chain) / len(core_chain) if core_chain else 0
        unique_ids = list(set(seg.id for seg in core_chain))
        
        print(f"RTK轨迹时长: {(rtk_end - rtk_start).total_seconds():.1f}s")
        print(f"匹配轨迹时长: {matched_duration:.1f}s")
        if core_chain:
            print(f"对齐时间范围: {aligned_duration:.1f}s")
        print(f"覆盖率: {coverage_rate:.1f}%")
        print(f"平均匹配分数: {avg_score:.3f}")
        print(f"涉及ID数量: {len(unique_ids)}")
        print(f"使用的ID: {unique_ids}")
        
        # 异常统计
        total_missing_duration = sum(gap['duration'] for gap in anomalies['missing_gaps'])
        print(f"\n异常统计:")
        print(f"  分裂事件: {len(anomalies['split_events'])} 次")
        print(f"  ID切换: {len(anomalies['id_switches'])} 次")
        print(f"  漏检总时长: {total_missing_duration:.1f}s")
        print(f"  拒绝段: {len(anomalies['rejected_segments'])} 个")
        
        print(f"\n✅ 匹配完成！输出文件已保存到: {output_dir}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
参数调优脚本
调优rtk_max_time_diff和pre_filter_threshold等关键参数，平衡性能和精度
"""

import sys
import os
import json
import time
import itertools
from datetime import datetime, timedelta
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.simple_distance_matcher import SimpleDistanceMatcher
from core.data_utils import RTKPoint, PerceptionPoint
from core.config_loader import Config


class ParameterTuner:
    """参数调优类"""
    
    def __init__(self):
        self.results = {
            'tuning_runs': [],
            'best_parameters': {},
            'parameter_analysis': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # 参数搜索空间
        self.parameter_space = {
            'rtk_max_time_diff': [1.0, 2.0, 3.0, 4.0, 5.0],
            'pre_filter_threshold': [0.0, 0.2, 0.3, 0.4, 0.5, 0.6]
        }
    
    def create_config_with_params(self, rtk_max_time_diff, pre_filter_threshold):
        """根据参数创建配置"""
        return Config({
            'performance': {
                'enable_time_aligned_search': True,
                'rtk_max_time_diff': rtk_max_time_diff,
                'enable_performance_logging': False  # 减少日志输出
            },
            'scoring': {
                'method': 'unified',
                'enable_score_cache': True,
                'pre_filter_threshold': pre_filter_threshold,
                'cache_debug_output': False
            },
            'matching': {
                'local_match_thr': 0.7,
                'min_segment_length': 2
            },
            'corridor': {
                'enable_corridor_filter': False
            }
        })
    
    def generate_test_data(self):
        """生成调优测试数据"""
        base_time = datetime.now()
        
        # 生成RTK轨迹
        rtk_points = []
        for i in range(1000):  # 10秒，100Hz
            rtk_point = RTKPoint(
                timestamp=base_time + timedelta(seconds=i * 0.01),
                lat=39.9 + i * 0.0001,
                lon=116.3 + i * 0.0001,
                alt=50.0,
                accuracy=0.1
            )
            rtk_points.append(rtk_point)
        
        # 生成感知轨迹（多目标，包含噪声）
        perception_points = []
        import random
        
        for obj_id in range(1, 6):  # 5个目标
            for i in range(100):  # 每个目标100个点
                noise_lat = random.uniform(-0.00003, 0.00003)
                noise_lon = random.uniform(-0.00003, 0.00003)
                time_offset = random.uniform(0, 8)
                
                per_point = PerceptionPoint(
                    timestamp=base_time + timedelta(seconds=time_offset + i * 0.02),
                    id=obj_id,
                    lat=39.9 + i * 0.0002 + obj_id * 0.0001 + noise_lat,
                    lon=116.3 + i * 0.0002 + obj_id * 0.0001 + noise_lon,
                    speed=random.uniform(8, 12),
                    heading=random.uniform(85, 95)
                )
                perception_points.append(per_point)
        
        return rtk_points, perception_points
    
    def evaluate_parameters(self, rtk_points, perception_points, rtk_max_time_diff, pre_filter_threshold):
        """评估参数组合的性能"""
        config = self.create_config_with_params(rtk_max_time_diff, pre_filter_threshold)
        
        # 运行匹配
        start_time = time.perf_counter()
        matcher = SimpleDistanceMatcher(config, rtk_points)
        
        filtered_perception = matcher.filter_perception_points(perception_points)
        segments = matcher.build_segments(filtered_perception)
        core_chain = matcher.build_core_chain(segments)
        
        execution_time = time.perf_counter() - start_time
        
        # 计算质量指标
        if core_chain:
            avg_score = sum(getattr(seg, 'final_score', 0) for seg in core_chain) / len(core_chain)
            total_duration = sum(seg.duration for seg in core_chain)
            coverage_rate = total_duration / 10.0  # 相对于10秒RTK轨迹的覆盖率
        else:
            avg_score = 0.0
            total_duration = 0.0
            coverage_rate = 0.0
        
        # 获取缓存统计
        cache_stats = matcher.score_cache.get_stats()
        
        return {
            'rtk_max_time_diff': rtk_max_time_diff,
            'pre_filter_threshold': pre_filter_threshold,
            'execution_time': execution_time,
            'avg_score': avg_score,
            'core_chain_count': len(core_chain),
            'total_duration': total_duration,
            'coverage_rate': coverage_rate,
            'cache_hit_rate': cache_stats.get('hit_rate', 0),
            'segments_before_filter': len(segments),
            'segments_after_filter': len(core_chain)
        }
    
    def run_grid_search(self):
        """运行网格搜索调优"""
        print("开始参数网格搜索调优...")
        print(f"搜索空间: {self.parameter_space}")
        
        # 生成测试数据
        rtk_points, perception_points = self.generate_test_data()
        print(f"测试数据: RTK={len(rtk_points)}, 感知={len(perception_points)}")
        
        # 网格搜索
        total_combinations = len(self.parameter_space['rtk_max_time_diff']) * len(self.parameter_space['pre_filter_threshold'])
        current_combination = 0
        
        for rtk_time_diff, pre_filter_thresh in itertools.product(
            self.parameter_space['rtk_max_time_diff'],
            self.parameter_space['pre_filter_threshold']
        ):
            current_combination += 1
            print(f"\n进度: {current_combination}/{total_combinations}")
            print(f"测试参数: rtk_max_time_diff={rtk_time_diff}, pre_filter_threshold={pre_filter_thresh}")
            
            try:
                result = self.evaluate_parameters(
                    rtk_points, perception_points, 
                    rtk_time_diff, pre_filter_thresh
                )
                
                # 计算综合评分（平衡性能和精度）
                performance_score = max(0, 10 - result['execution_time'])  # 性能分（越快越好）
                accuracy_score = result['avg_score'] * 10  # 精度分
                coverage_score = result['coverage_rate'] * 10  # 覆盖率分
                
                # 综合评分权重
                result['composite_score'] = (
                    performance_score * 0.4 +  # 性能权重40%
                    accuracy_score * 0.4 +     # 精度权重40%
                    coverage_score * 0.2       # 覆盖率权重20%
                )
                
                self.results['tuning_runs'].append(result)
                
                print(f"  执行时间: {result['execution_time']:.3f}s")
                print(f"  平均评分: {result['avg_score']:.3f}")
                print(f"  覆盖率: {result['coverage_rate']:.1%}")
                print(f"  综合评分: {result['composite_score']:.2f}")
                
            except Exception as e:
                print(f"  参数组合失败: {e}")
                continue
    
    def analyze_results(self):
        """分析调优结果"""
        if not self.results['tuning_runs']:
            print("没有有效的调优结果")
            return
        
        # 找到最佳参数组合
        best_result = max(self.results['tuning_runs'], key=lambda x: x['composite_score'])
        self.results['best_parameters'] = {
            'rtk_max_time_diff': best_result['rtk_max_time_diff'],
            'pre_filter_threshold': best_result['pre_filter_threshold'],
            'performance_metrics': {
                'execution_time': best_result['execution_time'],
                'avg_score': best_result['avg_score'],
                'coverage_rate': best_result['coverage_rate'],
                'composite_score': best_result['composite_score']
            }
        }
        
        # 参数敏感性分析
        self.analyze_parameter_sensitivity()
        
        print(f"\n{'='*60}")
        print("参数调优结果分析")
        print(f"{'='*60}")
        print(f"最佳参数组合:")
        print(f"  rtk_max_time_diff: {best_result['rtk_max_time_diff']}")
        print(f"  pre_filter_threshold: {best_result['pre_filter_threshold']}")
        print(f"性能指标:")
        print(f"  执行时间: {best_result['execution_time']:.3f}s")
        print(f"  平均评分: {best_result['avg_score']:.3f}")
        print(f"  覆盖率: {best_result['coverage_rate']:.1%}")
        print(f"  综合评分: {best_result['composite_score']:.2f}")
    
    def analyze_parameter_sensitivity(self):
        """分析参数敏感性"""
        # 分析rtk_max_time_diff的影响
        rtk_time_analysis = {}
        for time_diff in self.parameter_space['rtk_max_time_diff']:
            matching_runs = [r for r in self.results['tuning_runs'] if r['rtk_max_time_diff'] == time_diff]
            if matching_runs:
                avg_performance = sum(r['execution_time'] for r in matching_runs) / len(matching_runs)
                avg_accuracy = sum(r['avg_score'] for r in matching_runs) / len(matching_runs)
                rtk_time_analysis[time_diff] = {
                    'avg_execution_time': avg_performance,
                    'avg_accuracy': avg_accuracy,
                    'sample_count': len(matching_runs)
                }
        
        # 分析pre_filter_threshold的影响
        filter_threshold_analysis = {}
        for threshold in self.parameter_space['pre_filter_threshold']:
            matching_runs = [r for r in self.results['tuning_runs'] if r['pre_filter_threshold'] == threshold]
            if matching_runs:
                avg_performance = sum(r['execution_time'] for r in matching_runs) / len(matching_runs)
                avg_accuracy = sum(r['avg_score'] for r in matching_runs) / len(matching_runs)
                filter_threshold_analysis[threshold] = {
                    'avg_execution_time': avg_performance,
                    'avg_accuracy': avg_accuracy,
                    'sample_count': len(matching_runs)
                }
        
        self.results['parameter_analysis'] = {
            'rtk_max_time_diff_sensitivity': rtk_time_analysis,
            'pre_filter_threshold_sensitivity': filter_threshold_analysis
        }
        
        print(f"\n参数敏感性分析:")
        print(f"rtk_max_time_diff影响:")
        for time_diff, analysis in rtk_time_analysis.items():
            print(f"  {time_diff}s: 平均时间={analysis['avg_execution_time']:.3f}s, 平均精度={analysis['avg_accuracy']:.3f}")
        
        print(f"pre_filter_threshold影响:")
        for threshold, analysis in filter_threshold_analysis.items():
            print(f"  {threshold}: 平均时间={analysis['avg_execution_time']:.3f}s, 平均精度={analysis['avg_accuracy']:.3f}")
    
    def generate_recommendations(self):
        """生成参数推荐"""
        if not self.results['best_parameters']:
            return
        
        best_params = self.results['best_parameters']
        
        recommendations = {
            'production_config': {
                'rtk_max_time_diff': best_params['rtk_max_time_diff'],
                'pre_filter_threshold': best_params['pre_filter_threshold'],
                'rationale': f"基于网格搜索，该参数组合获得最高综合评分 {best_params['performance_metrics']['composite_score']:.2f}"
            },
            'alternative_configs': {
                'high_performance': {
                    'rtk_max_time_diff': 2.0,  # 较小的时间窗口，更快
                    'pre_filter_threshold': 0.5,  # 更严格的预裁剪
                    'use_case': '对性能要求极高的场景'
                },
                'high_accuracy': {
                    'rtk_max_time_diff': 4.0,  # 较大的时间窗口，更准确
                    'pre_filter_threshold': 0.2,  # 较宽松的预裁剪
                    'use_case': '对精度要求极高的场景'
                }
            }
        }
        
        self.results['recommendations'] = recommendations
        
        print(f"\n{'='*60}")
        print("参数推荐")
        print(f"{'='*60}")
        print(f"生产环境推荐配置:")
        print(f"  rtk_max_time_diff: {recommendations['production_config']['rtk_max_time_diff']}")
        print(f"  pre_filter_threshold: {recommendations['production_config']['pre_filter_threshold']}")
        print(f"  理由: {recommendations['production_config']['rationale']}")
    
    def save_results(self, output_path="tests/parameter_tuning_results.json"):
        """保存调优结果"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n参数调优结果已保存到: {output_path}")


def main():
    """主函数"""
    print("开始参数调优...")
    
    tuner = ParameterTuner()
    tuner.run_grid_search()
    tuner.analyze_results()
    tuner.generate_recommendations()
    tuner.save_results()
    
    print("\n参数调优完成!")


if __name__ == '__main__':
    main()

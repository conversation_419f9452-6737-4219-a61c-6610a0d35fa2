# 🚀 快速开始指南

## 📋 5分钟快速上手

### 1️⃣ 环境准备（1分钟）

```bash
# 确保Python 3.7+已安装
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2️⃣ 单文件处理测试（2分钟）

```bash
# 基本测试 - 处理一对文件
python main.py --rtk data/31.log --perception data/save_1753355915725.txt --config config/unified_config.json

# 查看结果
ls output/results/
```

**预期输出：**
- `31_save_1753355915725_trajectory_matched.csv` - 匹配结果
- `31_save_1753355915725_diagnostic.json` - 诊断信息
- `reports/` 目录 - 详细报告

### 3️⃣ 批量处理测试（2分钟）

```bash
# 串行批量处理（推荐新手）
python batch_simple.py --batch data/batch.csv --config config/unified_config.json

# 并行批量处理（推荐生产）
python batch_parallel_linux_optimized.py --batch data/batch.csv --config config/unified_config.json
```

**预期输出：**
```
✅ 批量处理完成！
📊 总任务数: 6
✅ 成功任务: 6
❌ 失败任务: 0
📈 成功率: 100.0%
⏱️  总耗时: 2.57秒
```

## 🎯 选择合适的处理方式

### 场景1：单次分析
```bash
# 分析单对文件
python main.py --rtk data/31.log --perception data/save_1753355915725.txt --config config/unified_config.json
```

### 场景2：小批量数据（<10个任务）
```bash
# 简单稳定的串行处理
python batch_simple.py --batch data/batch.csv --config config/unified_config.json
```

### 场景3：大批量数据（≥10个任务）
```bash
# 高性能并行处理
python batch_parallel_linux_optimized.py --batch data/batch.csv --config config/unified_config.json --workers 4
```

## 📊 性能参考

| 处理方式 | 6个任务耗时 | 适用场景 |
|---------|------------|----------|
| 单文件处理 | N/A | 单次分析 |
| 串行批量 | 3.58秒 | 小批量数据 |
| 并行批量 | 2.57秒 | 大批量数据 |

## 🔧 常用配置

### 基本配置文件
使用 `config/unified_config.json`，包含所有必要参数。

### 自定义输出目录
```bash
# 单文件处理
python main.py --rtk data/31.log --perception data/save_1753355915725.txt --config config/unified_config.json --output-dir output/my_analysis

# 批量处理
python batch_simple.py --batch data/batch.csv --config config/unified_config.json --output output/my_batch_results
```

### 详细模式
```bash
# 显示详细执行信息
python batch_parallel_linux_optimized.py --batch data/batch.csv --config config/unified_config.json --verbose
```

## 📁 批量任务配置

创建 `data/batch.csv` 文件：

```csv
test_file,rtk_file
.\data\save_1753355915725.txt,.\data\31.log
.\data\save_1753355915725.txt,.\data\32.log
.\data\save_1753355915725.txt,.\data\33.log
.\data\save_1753355915725.txt,.\data\34.log
.\data\save_1753355915725.txt,.\data\35.log
.\data\save_1753355915725.txt,.\data\36.log
```

## 📈 查看结果

### 单文件处理结果
```bash
# 查看匹配结果
head output/results/*_trajectory_matched.csv

# 查看诊断信息
cat output/results/*_diagnostic.json
```

### 批量处理结果
```bash
# 查看汇总报告
cat output/batch_*/batch_*_summary.json

# 查看CSV汇总
head output/batch_*/batch_*_summary.csv
```

## ❓ 常见问题

### Q1: 文件路径问题
**A:** 确保数据文件路径正确，使用相对路径或绝对路径。

### Q2: 内存不足
**A:** 减少并行进程数：
```bash
python batch_parallel_linux_optimized.py --batch data/batch.csv --workers 2
```

### Q3: 处理速度慢
**A:** 使用并行处理：
```bash
python batch_parallel_linux_optimized.py --batch data/batch.csv --workers 4
```

### Q4: 配置文件错误
**A:** 使用默认配置：
```bash
python main.py --rtk data/31.log --perception data/save_1753355915725.txt --config config/default.json
```

## 📚 进一步学习

- [📖 完整README](README.md) - 详细功能介绍
- [📋 批量处理指南](BATCH_PROCESSING_GUIDE.md) - 批量处理详细说明
- [⚙️ 配置参考](docs/config_reference.md) - 配置文件详解
- [📊 性能报告](性能测试对比报告.md) - 性能测试结果

## 🎉 开始使用

现在您已经掌握了基本使用方法，可以开始处理您的数据了！

**推荐流程：**
1. 先用单文件处理测试一下
2. 确认结果正确后使用批量处理
3. 根据数据量选择串行或并行处理
4. 查看详细文档了解高级功能

"""
数据处理工具模块
包含时间同步、坐标转换、数据验证等功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timezone
import pytz
from typing import Tuple, List, Dict, Optional
import logging
import json
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RTKPoint:
    """RTK轨迹点数据类"""
    timestamp: datetime
    lat: float
    lon: float
    speed: float
    heading: float

@dataclass
class PerceptionPoint:
    """感知数据点数据类"""
    timestamp: datetime
    id: str
    lat: float
    lon: float
    speed: float
    heading: float

class Config:
    """配置类"""
    
    def __init__(self, **kwargs):
        # 处理嵌套配置结构
        roi_config = kwargs.get('roi', {})
        corridor_config = kwargs.get('corridor', {})
        matching_config = kwargs.get('matching', {})
        scoring_config = kwargs.get('scoring', {})
        anomaly_config = kwargs.get('anomaly', {})
        processing_config = kwargs.get('processing', {})
        output_config = kwargs.get('output', {})
        
        # 存储其他配置信息（如description, version等）
        self.description = kwargs.get('description', '')
        self.version = kwargs.get('version', '1.0')
        self.profiles = kwargs.get('profiles', {})
        
        # ROI过滤参数
        self.roi_long = roi_config.get('roi_long', kwargs.get('roi_long', 20.0))
        self.roi_lat = roi_config.get('roi_lat', kwargs.get('roi_lat', 5.0))
        
        # 走廊过滤参数
        self.corridor_enabled = corridor_config.get('enabled', kwargs.get('enabled', True))
        self.corridor_fallback_to_roi = corridor_config.get('fallback_to_roi', kwargs.get('fallback_to_roi', True))
        self.corridor_downsample_interval_meters = corridor_config.get('downsample_interval_meters', kwargs.get('downsample_interval_meters', 10.0))
        self.corridor_long_buffer_meters = corridor_config.get('long_buffer_meters', kwargs.get('long_buffer_meters', 25.0))
        self.corridor_lat_buffer_meters = corridor_config.get('lat_buffer_meters', kwargs.get('lat_buffer_meters', 5.0))
        self.corridor_time_buffer_seconds = corridor_config.get('time_buffer_seconds', kwargs.get('time_buffer_seconds', 3600.0))
        self.corridor_min_trajectory_points = corridor_config.get('min_trajectory_points', kwargs.get('min_trajectory_points', 3))
        self.corridor_min_trajectory_duration = corridor_config.get('min_trajectory_duration', kwargs.get('min_trajectory_duration', 0.5))
        self.corridor_direction_threshold_degrees = corridor_config.get('direction_threshold_degrees', kwargs.get('direction_threshold_degrees', 60.0))
        
        # 匹配参数
        self.win_sec = matching_config.get('win_sec', kwargs.get('win_sec', 3.0))
        self.local_match_thr = matching_config.get('local_match_thr', kwargs.get('local_match_thr', 0.8))
        self.split_match_thr = matching_config.get('split_match_thr', kwargs.get('split_match_thr', 0.7))
        self.overlap_min = matching_config.get('overlap_min', kwargs.get('overlap_min', 0.5))
        self.max_gap = matching_config.get('max_gap', kwargs.get('max_gap', 2.0))
        self.gap_match_thr = matching_config.get('gap_match_thr', kwargs.get('gap_match_thr', 0.5))
        self.max_missing_gap = matching_config.get('max_missing_gap', kwargs.get('max_missing_gap', 5.0))
        self.min_missing_gap = matching_config.get('min_missing_gap', kwargs.get('min_missing_gap', 0.5))
        self.rtk_buffer = matching_config.get('rtk_buffer', kwargs.get('rtk_buffer', 5.0))
        self.good_match_thr = matching_config.get('good_match_thr', kwargs.get('good_match_thr', 0.6))
        self.min_segment_length = matching_config.get('min_segment_length', kwargs.get('min_segment_length', 2))
        
        # 异常检测参数
        self.switch_dt = anomaly_config.get('switch_dt', kwargs.get('switch_dt', 2.0))
        self.switch_dist = anomaly_config.get('switch_dist', kwargs.get('switch_dist', 10.0))
        self.switch_speed = anomaly_config.get('switch_speed', kwargs.get('switch_speed', 5.0))
        self.switch_heading = anomaly_config.get('switch_heading', kwargs.get('switch_heading', 30.0))
        
        # 评分权重
        self.peak_weight = scoring_config.get('peak_weight', kwargs.get('peak_weight', 0.6))
        self.duration_weight = scoring_config.get('duration_weight', kwargs.get('duration_weight', 0.3))
        self.stability_weight = scoring_config.get('stability_weight', kwargs.get('stability_weight', 0.1))

    def get(self, key: str, default=None):
        """获取配置值，支持属性访问方式的兼容性"""
        return getattr(self, key, default)

    @classmethod
    def from_json(cls, json_path: str):
        """从JSON文件加载配置"""
        with open(json_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 处理嵌套的JSON结构
        config_dict = {}
        if isinstance(config_data, dict):
            # 扁平化嵌套结构
            for key, value in config_data.items():
                if isinstance(value, dict):
                    # 为嵌套字典的内容添加前缀
                    if key == 'corridor':
                        for sub_key, sub_value in value.items():
                            config_dict[f'corridor_{sub_key}'] = sub_value
                    else:
                        # 将其他嵌套字典的内容展开到顶层
                        config_dict.update(value)
                else:
                    config_dict[key] = value
        else:
            config_dict = config_data
            
        return cls(**config_dict)
    
    def to_json(self, json_path: str):
        """保存配置到JSON文件"""
        config_dict = {
            'roi_long': self.roi_long,
            'roi_lat': self.roi_lat,
            'win_sec': self.win_sec,
            'local_match_thr': self.local_match_thr,
            'split_match_thr': self.split_match_thr,
            'overlap_min': self.overlap_min,
            'max_gap': self.max_gap,
            'switch_dt': self.switch_dt,
            'switch_dist': self.switch_dist,
            'switch_speed': self.switch_speed,
            'switch_heading': self.switch_heading,
            'gap_match_thr': self.gap_match_thr,
            'max_missing_gap': self.max_missing_gap,
            'min_missing_gap': self.min_missing_gap,
            'rtk_buffer': self.rtk_buffer,
            'good_match_thr': self.good_match_thr,
            'peak_weight': self.peak_weight,
            'duration_weight': self.duration_weight,
            'stability_weight': self.stability_weight,
            'min_segment_length': self.min_segment_length
        }
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

class DataLoader:
    """数据加载器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.time_sync = TimeSync()
    
    def load_rtk_csv(self, csv_path: str) -> List[RTKPoint]:
        """加载RTK CSV文件"""
        try:
            df = pd.read_csv(csv_path)
            
            # 验证必要字段
            required_fields = ['timestamp', 'lat', 'lon', 'speed', 'heading']
            missing_fields = [field for field in required_fields if field not in df.columns]
            if missing_fields:
                raise ValueError(f"RTK CSV缺少必要字段: {missing_fields}")
            
            points = []
            for _, row in df.iterrows():
                try:
                    # 解析时间戳
                    timestamp = datetime.fromisoformat(row['timestamp'].replace('Z', '+00:00'))
                    
                    point = RTKPoint(
                        timestamp=timestamp,
                        lat=float(row['lat']),
                        lon=float(row['lon']),
                        speed=float(row['speed']),
                        heading=float(row['heading'])
                    )
                    points.append(point)
                except Exception as e:
                    logger.warning(f"跳过无效RTK数据行: {e}")
                    continue
            
            return points
            
        except Exception as e:
            raise ValueError(f"加载RTK CSV失败: {e}")
    
    def load_perception_csv(self, csv_path: str) -> List[PerceptionPoint]:
        """加载感知数据CSV文件"""
        try:
            df = pd.read_csv(csv_path)
            
            # 验证必要字段
            required_fields = ['timestamp', 'id', 'lat', 'lon', 'speed', 'heading']
            missing_fields = [field for field in required_fields if field not in df.columns]
            if missing_fields:
                raise ValueError(f"感知CSV缺少必要字段: {missing_fields}")
            
            points = []
            for _, row in df.iterrows():
                try:
                    # 解析时间戳
                    timestamp = datetime.fromisoformat(row['timestamp'].replace('Z', '+00:00'))
                    
                    point = PerceptionPoint(
                        timestamp=timestamp,
                        id=str(row['id']),
                        lat=float(row['lat']),
                        lon=float(row['lon']),
                        speed=float(row['speed']),
                        heading=float(row['heading'])
                    )
                    points.append(point)
                except Exception as e:
                    logger.warning(f"跳过无效感知数据行: {e}")
                    continue
            
            return points
            
        except Exception as e:
            raise ValueError(f"加载感知CSV失败: {e}")
    
    def sync_rtk_time(self, rtk_points: List[RTKPoint]) -> List[RTKPoint]:
        """同步RTK时间（UTC转北京时间）"""
        synced_points = []
        for point in rtk_points:
            try:
                # 如果是UTC时间，转换为北京时间
                if point.timestamp.tzinfo is None:
                    # 假设无时区信息的时间为UTC
                    utc_time = point.timestamp.replace(tzinfo=pytz.UTC)
                else:
                    utc_time = point.timestamp
                
                beijing_time = self.time_sync.utc_to_beijing(utc_time)
                
                synced_point = RTKPoint(
                    timestamp=beijing_time,
                    lat=point.lat,
                    lon=point.lon,
                    speed=point.speed,
                    heading=point.heading
                )
                synced_points.append(synced_point)
            except Exception as e:
                logger.warning(f"时间同步失败: {e}")
                continue
        
        return synced_points
    
    def sync_perception_time(self, perception_points: List[PerceptionPoint]) -> List[PerceptionPoint]:
        """同步感知时间（假设已是北京时间）"""
        synced_points = []
        for point in perception_points:
            try:
                # 如果没有时区信息，假设为北京时间
                if point.timestamp.tzinfo is None:
                    beijing_time = self.time_sync.beijing_tz.localize(point.timestamp)
                else:
                    beijing_time = point.timestamp
                
                synced_point = PerceptionPoint(
                    timestamp=beijing_time,
                    id=point.id,
                    lat=point.lat,
                    lon=point.lon,
                    speed=point.speed,
                    heading=point.heading
                )
                synced_points.append(synced_point)
            except Exception as e:
                logger.warning(f"时间同步失败: {e}")
                continue
        
        return synced_points

class TimeSync:
    """时间同步工具类"""
    
    def __init__(self):
        self.beijing_tz = pytz.timezone('Asia/Shanghai')
        self.utc_tz = pytz.UTC
    
    def utc_to_beijing(self, utc_timestamp: str) -> datetime:
        """UTC时间转北京时间"""
        try:
            # 解析UTC时间戳
            if isinstance(utc_timestamp, str):
                # 尝试多种时间格式
                formats = [
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S.%fZ',
                    '%Y-%m-%dT%H:%M:%SZ'
                ]
                
                dt = None
                for fmt in formats:
                    try:
                        dt = datetime.strptime(utc_timestamp, fmt)
                        break
                    except ValueError:
                        continue
                
                if dt is None:
                    raise ValueError(f"无法解析时间格式: {utc_timestamp}")
                
                # 设置为UTC时区
                dt = dt.replace(tzinfo=self.utc_tz)
            else:
                dt = utc_timestamp
            
            # 转换为北京时间
            beijing_dt = dt.astimezone(self.beijing_tz)
            return beijing_dt
            
        except Exception as e:
            logger.error(f"时间转换错误: {utc_timestamp} -> {e}")
            raise
    
    def beijing_to_utc(self, beijing_timestamp: str) -> datetime:
        """北京时间转UTC时间"""
        try:
            # 解析北京时间戳
            if isinstance(beijing_timestamp, str):
                formats = [
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%d %H:%M:%S',
                ]
                
                dt = None
                for fmt in formats:
                    try:
                        dt = datetime.strptime(beijing_timestamp, fmt)
                        break
                    except ValueError:
                        continue
                
                if dt is None:
                    raise ValueError(f"无法解析时间格式: {beijing_timestamp}")
                
                # 设置为北京时区
                dt = self.beijing_tz.localize(dt)
            else:
                dt = beijing_timestamp
            
            # 转换为UTC时间
            utc_dt = dt.astimezone(self.utc_tz)
            return utc_dt
            
        except Exception as e:
            logger.error(f"时间转换错误: {beijing_timestamp} -> {e}")
            raise
    
    def align_timestamps(self, rtk_df: pd.DataFrame, perception_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """时间戳对齐"""
        logger.info("执行时间戳对齐...")
        
        # 复制数据框
        rtk_aligned = rtk_df.copy()
        perception_aligned = perception_df.copy()
        
        # 检测并转换RTK时间戳（假设是UTC）
        if 'timestamp' in rtk_aligned.columns:
            logger.info("转换RTK时间戳 (UTC -> 北京时间)")
            rtk_aligned['timestamp'] = rtk_aligned['timestamp'].apply(self.utc_to_beijing)
        
        # 检测并转换感知时间戳（假设是北京时间）
        if 'timestamp' in perception_aligned.columns:
            logger.info("标准化感知时间戳 (北京时间)")
            perception_aligned['timestamp'] = pd.to_datetime(perception_aligned['timestamp'])
            # 如果没有时区信息，设置为北京时区
            if perception_aligned['timestamp'].dt.tz is None:
                perception_aligned['timestamp'] = perception_aligned['timestamp'].dt.tz_localize(self.beijing_tz)
        
        return rtk_aligned, perception_aligned

class GeoUtils:
    """地理坐标工具类"""
    
    @staticmethod
    def haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间的球面距离(米)"""
        R = 6371000  # 地球半径(米)
        
        # 转换为弧度
        lat1_rad = np.radians(lat1)
        lon1_rad = np.radians(lon1)
        lat2_rad = np.radians(lat2)
        lon2_rad = np.radians(lon2)
        
        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        # Haversine公式
        a = np.sin(dlat/2)**2 + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c

    @staticmethod
    def haversine_distance_rad(lat1_rad: float, lon1_rad: float, lat2_rad: float, lon2_rad: float) -> float:
        """计算两点间的球面距离(米) - 弧度制输入版本，用于性能优化"""
        R = 6371000  # 地球半径(米)

        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad

        # Haversine公式
        a = np.sin(dlat/2)**2 + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))

        return R * c

    @staticmethod
    def calculate_heading(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间的航向角(度)"""
        # 转换为弧度
        lat1_rad = np.radians(lat1)
        lon1_rad = np.radians(lon1)
        lat2_rad = np.radians(lat2)
        lon2_rad = np.radians(lon2)
        
        # 计算航向角
        dlon = lon2_rad - lon1_rad
        y = np.sin(dlon) * np.cos(lat2_rad)
        x = np.cos(lat1_rad) * np.sin(lat2_rad) - np.sin(lat1_rad) * np.cos(lat2_rad) * np.cos(dlon)
        
        heading_rad = np.arctan2(y, x)
        heading_deg = np.degrees(heading_rad)
        
        # 转换为0-360度
        return (heading_deg + 360) % 360
    
    @staticmethod
    def point_in_rotated_rectangle(point_lat: float, point_lon: float,
                                 center_lat: float, center_lon: float,
                                 heading: float, length: float, width: float) -> bool:
        """判断点是否在以航向为朝向的矩形内"""
        # 简化实现：先用圆形近似
        distance = GeoUtils.haversine_distance(point_lat, point_lon, center_lat, center_lon)
        return distance <= max(length, width)
    
    @staticmethod
    def wgs84_to_utm(lat: float, lon: float) -> Tuple[float, float, int]:
        """
        将WGS84坐标转换为UTM坐标
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            (x, y, zone): UTM坐标和UTM带号
        """
        import math
        
        # 计算UTM带号
        zone = int((lon + 180) / 6) + 1
        
        # UTM投影参数
        a = 6378137.0  # WGS84长半轴
        f = 1/298.257223563  # WGS84扁率
        k0 = 0.9996  # UTM比例因子
        
        # 计算偏心率
        e = math.sqrt(2*f - f*f)
        e2 = e*e
        
        # 转换为弧度
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        
        # 中央经线
        lon0_rad = math.radians((zone - 1) * 6 - 180 + 3)
        
        # 计算UTM坐标
        N = a / math.sqrt(1 - e2 * math.sin(lat_rad)**2)
        T = math.tan(lat_rad)**2
        C = e2 * math.cos(lat_rad)**2 / (1 - e2)
        A = math.cos(lat_rad) * (lon_rad - lon0_rad)
        
        M = a * ((1 - e2/4 - 3*e2*e2/64 - 5*e2*e2*e2/256) * lat_rad
                - (3*e2/8 + 3*e2*e2/32 + 45*e2*e2*e2/1024) * math.sin(2*lat_rad)
                + (15*e2*e2/256 + 45*e2*e2*e2/1024) * math.sin(4*lat_rad)
                - (35*e2*e2*e2/3072) * math.sin(6*lat_rad))
        
        x = k0 * N * (A + (1-T+C)*A**3/6 + (5-18*T+T*T+72*C-58*e2)*A**5/120) + 500000.0
        y = k0 * (M + N*math.tan(lat_rad)*(A*A/2 + (5-T+9*C+4*C*C)*A**4/24 + (61-58*T+T*T+600*C-330*e2)*A**6/720))
        
        # 南半球需要加上偏移
        if lat < 0:
            y += 10000000.0
            
        return x, y, zone

class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_rtk_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """验证RTK数据格式"""
        errors = []
        
        # 检查必需字段
        required_fields = ['timestamp', 'lat', 'lon', 'speed', 'heading']
        for field in required_fields:
            if field not in df.columns:
                errors.append(f"缺少必需字段: {field}")
        
        if errors:
            return False, errors
        
        # 检查数据类型和范围
        if df['lat'].dtype not in ['float64', 'float32']:
            errors.append("纬度字段类型错误")
        
        if df['lon'].dtype not in ['float64', 'float32']:
            errors.append("经度字段类型错误")
        
        if (df['lat'] < -90).any() or (df['lat'] > 90).any():
            errors.append("纬度值超出范围(-90, 90)")
        
        if (df['lon'] < -180).any() or (df['lon'] > 180).any():
            errors.append("经度值超出范围(-180, 180)")
        
        if (df['speed'] < 0).any():
            errors.append("速度值不能为负")
        
        if (df['heading'] < 0).any() or (df['heading'] >= 360).any():
            errors.append("航向值超出范围[0, 360)")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_perception_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """验证感知数据格式"""
        errors = []
        
        # 检查必需字段
        required_fields = ['timestamp', 'id', 'lat', 'lon', 'speed', 'heading']
        for field in required_fields:
            if field not in df.columns:
                errors.append(f"缺少必需字段: {field}")
        
        if errors:
            return False, errors
        
        # 检查数据类型和范围
        if df['lat'].dtype not in ['float64', 'float32']:
            errors.append("纬度字段类型错误")
        
        if df['lon'].dtype not in ['float64', 'float32']:
            errors.append("经度字段类型错误")
        
        if (df['lat'] < -90).any() or (df['lat'] > 90).any():
            errors.append("纬度值超出范围(-90, 90)")
        
        if (df['lon'] < -180).any() or (df['lon'] > 180).any():
            errors.append("经度值超出范围(-180, 180)")
        
        if (df['speed'] < 0).any():
            errors.append("速度值不能为负")
        
        if (df['heading'] < 0).any() or (df['heading'] >= 360).any():
            errors.append("航向值超出范围[0, 360)")
        
        # 检查ID字段
        if df['id'].isnull().any():
            errors.append("ID字段包含空值")
        
        return len(errors) == 0, errors

def preprocess_data(rtk_df: pd.DataFrame, perception_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """数据预处理主函数"""
    logger.info("开始数据预处理...")
    
    # 1. 数据验证
    validator = DataValidator()
    
    rtk_valid, rtk_errors = validator.validate_rtk_data(rtk_df)
    if not rtk_valid:
        logger.error(f"RTK数据验证失败: {rtk_errors}")
        raise ValueError(f"RTK数据格式错误: {rtk_errors}")
    
    perception_valid, perception_errors = validator.validate_perception_data(perception_df)
    if not perception_valid:
        logger.error(f"感知数据验证失败: {perception_errors}")
        raise ValueError(f"感知数据格式错误: {perception_errors}")
    
    logger.info("数据格式验证通过")
    
    # 2. 时间同步
    time_sync = TimeSync()
    rtk_aligned, perception_aligned = time_sync.align_timestamps(rtk_df, perception_df)
    
    # 3. 数据清洗
    # 移除重复时间戳
    rtk_aligned = rtk_aligned.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
    perception_aligned = perception_aligned.drop_duplicates(subset=['timestamp', 'id']).sort_values(['timestamp', 'id'])
    
    # 移除异常值
    rtk_aligned = rtk_aligned.dropna()
    perception_aligned = perception_aligned.dropna()
    
    logger.info(f"预处理完成: RTK {len(rtk_aligned)} 条, 感知 {len(perception_aligned)} 条")
    
    return rtk_aligned, perception_aligned 
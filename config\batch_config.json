{"description": "批量处理配置文件", "version": "1.0", "batch_processing": {"mode": "serial", "max_workers": 4, "retry_count": 1, "timeout_seconds": 300, "continue_on_error": true, "output_structure": "grouped_by_task"}, "result_aggregation": {"generate_summary": true, "generate_comparison": false, "statistical_analysis": false, "export_formats": ["csv", "json"]}, "logging": {"level": "INFO", "file": "batch_processing.log", "console_output": true}}
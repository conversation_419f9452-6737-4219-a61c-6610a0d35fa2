#!/usr/bin/env python3
"""
精度对比测试
比较优化前后的轨迹匹配精度，确保优化不影响匹配质量
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.simple_distance_matcher import SimpleDistanceMatcher
from core.data_utils import RTKPoint, PerceptionPoint
from core.config_loader import Config


class AccuracyComparisonTest:
    """精度对比测试类"""
    
    def __init__(self):
        self.results = {
            'test_cases': [],
            'summary': {},
            'timestamp': datetime.now().isoformat()
        }
    
    def create_test_config(self, enable_optimization=True):
        """创建测试配置"""
        return Config({
            'performance': {
                'enable_time_aligned_search': enable_optimization,
                'rtk_max_time_diff': 3.0,
                'enable_performance_logging': True
            },
            'scoring': {
                'method': 'unified',
                'enable_score_cache': enable_optimization,
                'pre_filter_threshold': 0.4 if enable_optimization else 0.0,
                'cache_debug_output': False
            },
            'matching': {
                'local_match_thr': 0.7,
                'min_segment_length': 2
            },
            'corridor': {
                'enable_corridor_filter': False  # 简化测试
            }
        })
    
    def generate_test_data(self, scenario_name, rtk_count=200, perception_count=100):
        """生成测试数据"""
        base_time = datetime.now()
        
        # 生成RTK轨迹
        rtk_points = []
        for i in range(rtk_count):
            rtk_point = RTKPoint(
                timestamp=base_time + timedelta(seconds=i * 0.1),
                lat=39.9 + i * 0.0001,
                lon=116.3 + i * 0.0001,
                alt=50.0,
                accuracy=0.1
            )
            rtk_points.append(rtk_point)
        
        # 生成感知轨迹（模拟不同场景）
        perception_points = []
        
        if scenario_name == "perfect_match":
            # 完美匹配场景
            for i in range(perception_count):
                per_point = PerceptionPoint(
                    timestamp=base_time + timedelta(seconds=i * 0.2),
                    id=1,
                    lat=39.9 + i * 0.0002,  # 与RTK轨迹对齐
                    lon=116.3 + i * 0.0002,
                    speed=10.0,
                    heading=90.0
                )
                perception_points.append(per_point)
        
        elif scenario_name == "noisy_data":
            # 噪声数据场景
            import random
            for i in range(perception_count):
                noise_lat = random.uniform(-0.00005, 0.00005)
                noise_lon = random.uniform(-0.00005, 0.00005)
                per_point = PerceptionPoint(
                    timestamp=base_time + timedelta(seconds=i * 0.2),
                    id=1,
                    lat=39.9 + i * 0.0002 + noise_lat,
                    lon=116.3 + i * 0.0002 + noise_lon,
                    speed=10.0 + random.uniform(-2, 2),
                    heading=90.0 + random.uniform(-10, 10)
                )
                perception_points.append(per_point)
        
        elif scenario_name == "multi_object":
            # 多目标场景
            for obj_id in range(1, 4):  # 3个目标
                for i in range(perception_count // 3):
                    per_point = PerceptionPoint(
                        timestamp=base_time + timedelta(seconds=i * 0.3),
                        id=obj_id,
                        lat=39.9 + i * 0.0002 + obj_id * 0.0001,
                        lon=116.3 + i * 0.0002 + obj_id * 0.0001,
                        speed=10.0,
                        heading=90.0
                    )
                    perception_points.append(per_point)
        
        return rtk_points, perception_points
    
    def run_matching_test(self, rtk_points, perception_points, config, test_name):
        """运行匹配测试"""
        print(f"运行测试: {test_name}")
        
        # 创建匹配器
        matcher = SimpleDistanceMatcher(config, rtk_points)
        
        # 记录开始时间
        start_time = time.perf_counter()
        
        # 执行匹配流程
        filtered_perception = matcher.filter_perception_points(perception_points)
        segments = matcher.build_segments(filtered_perception)
        core_chain = matcher.build_core_chain(segments)
        
        # 记录结束时间
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # 收集结果
        result = {
            'test_name': test_name,
            'execution_time': execution_time,
            'input_perception_points': len(perception_points),
            'filtered_perception_points': len(filtered_perception),
            'segments_count': len(segments),
            'core_chain_count': len(core_chain),
            'total_matched_duration': sum(seg.duration for seg in core_chain),
            'average_segment_score': sum(getattr(seg, 'final_score', 0) for seg in core_chain) / len(core_chain) if core_chain else 0,
            'performance_stats': matcher.performance_monitor.get_stats() if hasattr(matcher, 'performance_monitor') else {},
            'cache_stats': matcher.score_cache.get_stats() if hasattr(matcher, 'score_cache') else {}
        }
        
        print(f"  执行时间: {execution_time:.3f}s")
        print(f"  核心链段数: {len(core_chain)}")
        print(f"  平均评分: {result['average_segment_score']:.3f}")
        
        return result
    
    def compare_scenarios(self):
        """比较不同场景下的精度"""
        scenarios = ["perfect_match", "noisy_data", "multi_object"]
        
        for scenario in scenarios:
            print(f"\n{'='*50}")
            print(f"测试场景: {scenario}")
            print(f"{'='*50}")
            
            # 生成测试数据
            rtk_points, perception_points = self.generate_test_data(scenario)
            
            # 测试优化版本
            optimized_config = self.create_test_config(enable_optimization=True)
            optimized_result = self.run_matching_test(
                rtk_points, perception_points, optimized_config, 
                f"{scenario}_optimized"
            )
            
            # 测试传统版本
            legacy_config = self.create_test_config(enable_optimization=False)
            legacy_result = self.run_matching_test(
                rtk_points, perception_points, legacy_config, 
                f"{scenario}_legacy"
            )
            
            # 计算精度差异
            accuracy_diff = abs(optimized_result['average_segment_score'] - legacy_result['average_segment_score'])
            performance_improvement = legacy_result['execution_time'] / optimized_result['execution_time']
            
            comparison = {
                'scenario': scenario,
                'optimized_result': optimized_result,
                'legacy_result': legacy_result,
                'accuracy_difference': accuracy_diff,
                'performance_improvement': performance_improvement,
                'accuracy_preserved': accuracy_diff < 0.05  # 5%的精度容差
            }
            
            self.results['test_cases'].append(comparison)
            
            print(f"\n精度对比:")
            print(f"  优化版评分: {optimized_result['average_segment_score']:.3f}")
            print(f"  传统版评分: {legacy_result['average_segment_score']:.3f}")
            print(f"  精度差异: {accuracy_diff:.3f}")
            print(f"  性能提升: {performance_improvement:.1f}×")
            print(f"  精度保持: {'✓' if comparison['accuracy_preserved'] else '✗'}")
    
    def generate_summary(self):
        """生成测试总结"""
        if not self.results['test_cases']:
            return
        
        total_tests = len(self.results['test_cases'])
        accuracy_preserved_count = sum(1 for case in self.results['test_cases'] if case['accuracy_preserved'])
        avg_performance_improvement = sum(case['performance_improvement'] for case in self.results['test_cases']) / total_tests
        max_accuracy_diff = max(case['accuracy_difference'] for case in self.results['test_cases'])
        
        self.results['summary'] = {
            'total_test_scenarios': total_tests,
            'accuracy_preserved_scenarios': accuracy_preserved_count,
            'accuracy_preservation_rate': accuracy_preserved_count / total_tests,
            'average_performance_improvement': avg_performance_improvement,
            'max_accuracy_difference': max_accuracy_diff,
            'test_passed': accuracy_preserved_count == total_tests
        }
        
        print(f"\n{'='*60}")
        print("测试总结")
        print(f"{'='*60}")
        print(f"总测试场景: {total_tests}")
        print(f"精度保持场景: {accuracy_preserved_count}/{total_tests}")
        print(f"精度保持率: {self.results['summary']['accuracy_preservation_rate']:.1%}")
        print(f"平均性能提升: {avg_performance_improvement:.1f}×")
        print(f"最大精度差异: {max_accuracy_diff:.3f}")
        print(f"测试结果: {'通过' if self.results['summary']['test_passed'] else '失败'}")
    
    def save_results(self, output_path="tests/accuracy_comparison_results.json"):
        """保存测试结果"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n测试结果已保存到: {output_path}")


def main():
    """主函数"""
    print("开始精度对比测试...")
    
    tester = AccuracyComparisonTest()
    tester.compare_scenarios()
    tester.generate_summary()
    tester.save_results()
    
    print("\n精度对比测试完成!")


if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
RTK走廊过滤器
基于多段线缓冲多边形的感知候选轨迹提取
实现 rtk_perception_candidate_extraction.md 中的方案
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
import logging

try:
    from shapely.geometry import LineString, Point, Polygon
    from shapely.ops import unary_union
    import shapely.prepared
    from shapely.strtree import STRtree
    SHAPELY_AVAILABLE = True
except ImportError:
    SHAPELY_AVAILABLE = False

from .data_utils import RTKPoint, PerceptionPoint, GeoUtils

logger = logging.getLogger(__name__)


class CorridorFilter:
    """RTK走廊过滤器"""
    
    def __init__(self, config):
        """
        初始化走廊过滤器
        
        Args:
            config: 配置对象，包含走廊过滤参数
        """
        self.config = config
        self.geo_utils = GeoUtils()
        
        # 走廊参数
        self.enabled = getattr(config, 'corridor_enabled', True)
        self.fallback_to_roi = getattr(config, 'corridor_fallback_to_roi', True)
        self.downsample_interval = getattr(config, 'corridor_downsample_interval_meters', 10.0)
        self.long_buffer = getattr(config, 'corridor_long_buffer_meters', 25.0)
        self.lat_buffer = getattr(config, 'corridor_lat_buffer_meters', 5.0)
        self.time_buffer = getattr(config, 'corridor_time_buffer_seconds', 5.0)
        self.min_traj_points = getattr(config, 'corridor_min_trajectory_points', 3)
        self.min_traj_duration = getattr(config, 'corridor_min_trajectory_duration', 0.5)
        self.direction_threshold = getattr(config, 'corridor_direction_threshold_degrees', 60.0)
        self.point_level_filter = getattr(config, 'corridor_point_level_filter', False)
        
        # 缓存
        self._corridor_cache = {}
        
        if not SHAPELY_AVAILABLE:
            logger.warning("Shapely不可用，走廊过滤将被禁用")
            self.enabled = False
    
    def filter_perception_points(self, perception_points: List[PerceptionPoint], 
                               rtk_points: List[RTKPoint]) -> List[PerceptionPoint]:
        """
        使用走廊过滤感知点
        
        Args:
            perception_points: 感知数据点列表
            rtk_points: RTK轨迹点列表
            
        Returns:
            过滤后的感知点列表
        """
        if not self.enabled or not SHAPELY_AVAILABLE:
            logger.info("走廊过滤已禁用，跳过处理")
            return perception_points
        
        if not rtk_points or not perception_points:
            logger.warning("RTK或感知数据为空，无法进行走廊过滤")
            return perception_points
        
        try:
            logger.info(f"开始走廊过滤，RTK点数: {len(rtk_points)}, 感知点数: {len(perception_points)}")

            # 1. 时间粗过滤
            filtered_by_time = self._filter_by_time(perception_points, rtk_points)
            logger.info(f"时间过滤后剩余感知点: {len(filtered_by_time)}")

            if not filtered_by_time:
                logger.warning("时间过滤后无剩余数据")
                return []

            # 2. 生成走廊多边形（两种模式共用）
            logger.info("生成RTK走廊多边形...")
            corridor_poly, prepared_corridor = self._generate_corridor(rtk_points)

            # 3. 根据过滤模式选择不同的处理方式
            if self.point_level_filter:
                logger.info("使用点级走廊过滤模式")
                return self._apply_point_level_filter(filtered_by_time, prepared_corridor, len(perception_points))
            else:
                logger.info("使用轨迹段走廊过滤模式")
                return self._apply_trajectory_level_filter(filtered_by_time, prepared_corridor, rtk_points, len(perception_points))

            
        except Exception as e:
            logger.error(f"走廊过滤出错: {e}")
            if self.fallback_to_roi:
                logger.info("回退到传统ROI过滤")
                return perception_points  # 返回原始数据，让调用者使用ROI过滤
            else:
                raise

    def _apply_point_level_filter(self, filtered_by_time, prepared_corridor, total_points):
        """应用点级过滤模式"""
        from collections import defaultdict
        from shapely.geometry import Point

        # 逐点空间判定
        raw_kept_points = []
        for p in filtered_by_time:
            try:
                x, y, _ = self.geo_utils.wgs84_to_utm(p.lat, p.lon)
                if prepared_corridor.contains(Point(x, y)):
                    raw_kept_points.append(p)
            except Exception as e:
                logger.debug(f"点级过滤失败，跳过点: {e}")
                continue

        # 按ID分组并滤除小于阈值的短片段
        id_groups = defaultdict(list)
        for p in raw_kept_points:
            id_groups[p.id].append(p)

        kept_points = []
        for pts in id_groups.values():
            if len(pts) >= self.min_traj_points:
                kept_points.extend(pts)

        logger.info(f"点级走廊过滤完成，输出感知点: {len(kept_points)} "
                   f"({len(kept_points)/total_points*100:.1f}%)；过滤掉的短片段ID数: {len([g for g in id_groups if len(id_groups[g]) < self.min_traj_points])}")
        return kept_points

    def _apply_trajectory_level_filter(self, filtered_by_time, prepared_corridor, rtk_points, total_points):
        """应用轨迹段级过滤模式"""
        # 创建感知轨迹段
        trajectories = self._create_perception_trajectories(filtered_by_time)
        logger.info(f"创建感知轨迹段: {len(trajectories)}")

        if not trajectories:
            logger.warning("未能创建有效的感知轨迹段")
            return []

        # 空间匹配
        matched_trajectories = self._match_trajectories_to_corridor(
            trajectories, prepared_corridor, rtk_points
        )

        # 提取匹配轨迹的所有点
        matched_points = []
        for traj_id in matched_trajectories:
            if traj_id in trajectories:
                matched_points.extend(trajectories[traj_id]['points'])

        logger.info(f"轨迹段走廊过滤完成，输出感知点: {len(matched_points)} "
                   f"({len(matched_points)/total_points*100:.1f}%)")

        return matched_points
    
    def _filter_by_time(self, perception_points: List[PerceptionPoint], 
                       rtk_points: List[RTKPoint]) -> List[PerceptionPoint]:
        """时间窗过滤"""
        if not rtk_points:
            return perception_points
        
        # 获取RTK时间范围
        rtk_start = min(p.timestamp for p in rtk_points)
        rtk_end = max(p.timestamp for p in rtk_points)
        
        # 扩展时间窗
        time_delta = timedelta(seconds=self.time_buffer)
        filter_start = rtk_start - time_delta
        filter_end = rtk_end + time_delta
        
        # 过滤感知点
        filtered = [
            p for p in perception_points
            if filter_start <= p.timestamp <= filter_end
        ]
        
        return filtered
    
    def _downsample_rtk_by_distance(self, rtk_points: List[RTKPoint]) -> Tuple[List[RTKPoint], np.ndarray]:
        """按距离降采样RTK轨迹"""
        if len(rtk_points) < 2:
            coords = np.array([[0, 0]]) if not rtk_points else np.array([[
                *self.geo_utils.wgs84_to_utm(rtk_points[0].lat, rtk_points[0].lon)[:2]
            ]])
            return rtk_points, coords
        
        # 转换为UTM坐标
        coords = []
        for point in rtk_points:
            x, y, zone = self.geo_utils.wgs84_to_utm(point.lat, point.lon)
            coords.append([x, y])
        coords = np.array(coords)
        
        # 计算累积距离
        distances = np.zeros(len(coords))
        for i in range(1, len(coords)):
            distances[i] = distances[i-1] + np.linalg.norm(coords[i] - coords[i-1])
        
        # 按距离间隔采样
        sampled_indices = [0]  # 总是保留第一个点
        current_distance = 0
        
        for i in range(1, len(distances)):
            if distances[i] >= current_distance + self.downsample_interval:
                sampled_indices.append(i)
                current_distance = distances[i]
        
        # 总是保留最后一个点
        if sampled_indices[-1] != len(rtk_points) - 1:
            sampled_indices.append(len(rtk_points) - 1)
        
        downsampled_points = [rtk_points[i] for i in sampled_indices]
        downsampled_coords = coords[sampled_indices]
        
        logger.debug(f"RTK降采样: {len(rtk_points)} -> {len(downsampled_points)} 点")
        return downsampled_points, downsampled_coords
    
    def _generate_corridor(self, rtk_points: List[RTKPoint]) -> Tuple[Polygon, any]:
        """生成走廊多边形"""
        # 检查缓存
        cache_key = (len(rtk_points), rtk_points[0].timestamp, rtk_points[-1].timestamp)
        if cache_key in self._corridor_cache:
            logger.debug("使用缓存的走廊多边形")
            return self._corridor_cache[cache_key]
        
        # 降采样RTK轨迹
        downsampled_points, coords = self._downsample_rtk_by_distance(rtk_points)
        
        if len(coords) < 2:
            raise ValueError("RTK点数不足，无法生成走廊")
        
        # 使用快速buffer方法生成走廊
        corridor_poly = self._generate_corridor_fast(coords)
        prepared_corridor = shapely.prepared.prep(corridor_poly)
        
        # 缓存结果
        result = (corridor_poly, prepared_corridor)
        self._corridor_cache[cache_key] = result
        
        logger.debug(f"走廊多边形生成完成，面积: {corridor_poly.area:.1f}m²")
        return result
    
    def _generate_corridor_fast(self, coords: np.ndarray) -> Polygon:
        """快速生成走廊多边形 - 一次buffer方法"""
        if len(coords) < 2:
            raise ValueError("坐标点数量不足")
        
        # 计算首尾方向向量并各延长
        u_head = (coords[1] - coords[0])
        u_head = u_head / np.linalg.norm(u_head)
        
        u_tail = (coords[-1] - coords[-2])
        u_tail = u_tail / np.linalg.norm(u_tail)
        
        head_pt = coords[0] - self.long_buffer * u_head
        tail_pt = coords[-1] + self.long_buffer * u_tail
        
        # 拼接延长点后一次buffer
        ext_coords = np.vstack([head_pt, coords, tail_pt])
        corridor_poly = LineString(ext_coords).buffer(
            self.lat_buffer,
            cap_style=2,   # Flat
            join_style=2   # Miter
        ).buffer(0)        # 清理几何
        
        if not corridor_poly.is_valid:
            logger.warning("生成的走廊多边形无效，尝试修复")
            corridor_poly = corridor_poly.buffer(0.1).buffer(-0.1)
        
        return corridor_poly
    
    def _create_perception_trajectories(self, perception_points: List[PerceptionPoint]) -> Dict:
        """创建感知轨迹段"""
        trajectories = {}
        
        # 按ID分组
        id_groups = defaultdict(list)
        for point in perception_points:
            id_groups[point.id].append(point)
        
        for obj_id, points in id_groups.items():
            # 按时间排序
            points.sort(key=lambda p: p.timestamp)
            
            # 过滤条件
            if len(points) < self.min_traj_points:
                continue
            
            duration = (points[-1].timestamp - points[0].timestamp).total_seconds()
            if duration < self.min_traj_duration:
                continue
            
            # 转换为UTM坐标
            coords = []
            for point in points:
                x, y, zone = self.geo_utils.wgs84_to_utm(point.lat, point.lon)
                coords.append([x, y])
            coords = np.array(coords)
            
            # 创建LineString
            if len(coords) >= 2:
                try:
                    trajectory = LineString(coords)
                    trajectories[obj_id] = {
                        'geometry': trajectory,
                        'coords': coords,
                        'points': points,
                        'length': trajectory.length,
                        'duration': duration
                    }
                except Exception as e:
                    logger.debug(f"创建轨迹{obj_id}失败: {e}")
                    continue
        
        return trajectories
    
    def _calculate_trajectory_direction(self, coords: np.ndarray) -> np.ndarray:
        """计算轨迹的整体方向向量"""
        if len(coords) < 2:
            return np.array([0.0, 0.0])
        
        # 使用起点到终点的向量作为整体方向
        direction = coords[-1] - coords[0]
        
        # 归一化方向向量
        norm = np.linalg.norm(direction)
        if norm > 0:
            return direction / norm
        else:
            return np.array([0.0, 0.0])
    
    def _check_direction_consistency(self, traj_coords: np.ndarray, rtk_coords: np.ndarray) -> bool:
        """检查轨迹方向是否与RTK轨迹方向一致"""
        # 计算两条轨迹的方向向量
        traj_direction = self._calculate_trajectory_direction(traj_coords)
        rtk_direction = self._calculate_trajectory_direction(rtk_coords)
        
        # 如果任一方向向量为零向量，返回False
        if np.linalg.norm(traj_direction) == 0 or np.linalg.norm(rtk_direction) == 0:
            return False
        
        # 计算两个方向向量的夹角
        dot_product = np.dot(traj_direction, rtk_direction)
        dot_product = np.clip(dot_product, -1.0, 1.0)  # 避免数值误差
        
        angle_rad = np.arccos(dot_product)
        angle_deg = np.degrees(angle_rad)
        
        return angle_deg <= self.direction_threshold
    
    def _match_trajectories_to_corridor(self, trajectories: Dict, prepared_corridor, 
                                      rtk_points: List[RTKPoint]) -> List:
        """匹配轨迹到走廊"""
        if not trajectories:
            return []
        
        # 获取RTK轨迹坐标用于方向检查
        rtk_coords = []
        for point in rtk_points:
            x, y, zone = self.geo_utils.wgs84_to_utm(point.lat, point.lon)
            rtk_coords.append([x, y])
        rtk_coords = np.array(rtk_coords)
        
        # 获取RTK时间范围
        rtk_start_time = min(p.timestamp for p in rtk_points)
        rtk_end_time = max(p.timestamp for p in rtk_points)
        
        # 构建空间索引
        geoms = []
        traj_ids = []
        for traj_id, traj_info in trajectories.items():
            geoms.append(traj_info['geometry'])
            traj_ids.append(traj_id)
        
        if not geoms:
            return []
        
        str_tree = STRtree(geoms)
        
        # 粗筛：bbox相交
        corridor_bounds = prepared_corridor.context.bounds
        candidate_indices = str_tree.query(Polygon.from_bounds(*corridor_bounds))
        
        matched_ids = []
        
        for idx in candidate_indices:
            if idx >= len(traj_ids):
                continue
                
            traj_id = traj_ids[idx]
            traj_info = trajectories[traj_id]
            geometry = traj_info['geometry']
            coords = traj_info['coords']
            points = traj_info['points']
            
            # 条件1: 轨迹完全在走廊内
            if not prepared_corridor.contains(geometry):
                continue
            
            # 条件2: 行走方向与RTK轨迹一致
            if not self._check_direction_consistency(coords, rtk_coords):
                continue
            
            # 条件3: 时间段重合检查
            traj_start_time = min(p.timestamp for p in points)
            traj_end_time = max(p.timestamp for p in points)
            
            # 检查时间段是否有重合（允许一定的时间缓冲）
            time_buffer_delta = timedelta(seconds=300)  # 5分钟缓冲
            rtk_start_buffered = rtk_start_time - time_buffer_delta
            rtk_end_buffered = rtk_end_time + time_buffer_delta
            
            # 时间段重合判断
            time_overlap = not (traj_end_time < rtk_start_buffered or traj_start_time > rtk_end_buffered)
            
            if not time_overlap:
                continue
            
            # 所有条件都满足
            matched_ids.append(traj_id)
        
        logger.debug(f"空间匹配结果: {len(matched_ids)}/{len(trajectories)} 轨迹匹配")
        return matched_ids
    
    def get_corridor_stats(self) -> Dict:
        """获取走廊过滤统计信息"""
        return {
            'enabled': self.enabled,
            'shapely_available': SHAPELY_AVAILABLE,
            'parameters': {
                'downsample_interval': self.downsample_interval,
                'long_buffer': self.long_buffer,
                'lat_buffer': self.lat_buffer,
                'time_buffer': self.time_buffer,
                'min_trajectory_points': self.min_traj_points,
                'min_trajectory_duration': self.min_traj_duration,
                'direction_threshold': self.direction_threshold
            },
            'cache_size': len(self._corridor_cache)
        } 
"""
批量处理HTML报告生成器
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import statistics


class BatchReportGenerator:
    """批量处理HTML报告生成器"""
    
    def __init__(self, template_path: str = "templates/batch_summary_template.html"):
        """
        初始化报告生成器
        
        Args:
            template_path: HTML模板文件路径
        """
        self.template_path = Path(template_path)
    
    def generate_html_report(self, summary_data: Dict, output_path: str) -> bool:
        """
        生成批量处理HTML报告
        
        Args:
            summary_data: 批量处理汇总数据
            output_path: 输出文件路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            # 检查模板文件是否存在
            if not self.template_path.exists():
                print(f"警告: 模板文件不存在 {self.template_path}")
                return False
            
            # 读取模板
            with open(self.template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 准备模板数据
            template_data = self._prepare_template_data(summary_data, output_path)
            
            # 替换模板变量
            report_content = self._render_template(template_content, template_data)
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入报告文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 批量处理HTML报告已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成批量处理HTML报告失败: {e}")
            return False
    
    def _prepare_template_data(self, summary_data: Dict, output_path: str) -> Dict[str, Any]:
        """准备模板数据"""
        try:
            # 基础统计信息
            data = {
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_tasks': summary_data.get('total_tasks', 0),
                'completed_tasks': summary_data.get('completed_tasks', 0),
                'failed_tasks': summary_data.get('failed_tasks', 0),
                'success_rate': f"{summary_data.get('success_rate', 0):.1f}",
                'total_duration': f"{summary_data.get('total_duration', 0):.2f}",
                'average_duration': f"{summary_data.get('average_duration', 0):.2f}"
            }

            # 处理任务详情
            tasks_data = []
            failed_task_details = []
            durations = []

            for result in summary_data.get('results', []):
                # 基本任务信息
                task_data = {
                    'task_id': result.get('task_id', ''),
                    'perception_file_name': Path(result.get('perception_file', '')).name,
                    'rtk_file_name': Path(result.get('rtk_file', '')).name,
                    'status_text': '成功' if result.get('status') == 'completed' else '失败',
                    'status_class': 'success' if result.get('status') == 'completed' else 'failed',
                    'duration': f"{result.get('duration', 0):.2f}",
                    'start_time_formatted': self._format_datetime(result.get('start_time', '')),
                    'error_message': result.get('error_message', '')
                }

                # 查找详细报告链接和JSON文件
                report_link = self._find_report_link(result, output_path)
                if report_link:
                    task_data['report_link'] = report_link

                json_link = self._find_json_link(result, output_path)
                if json_link:
                    task_data['json_link'] = json_link

                # 提取任务指标（仅对成功的任务）
                if result.get('status') == 'completed':
                    metrics = self._extract_task_metrics(result)
                    task_data.update(metrics)

                tasks_data.append(task_data)

                # 收集失败任务详情
                if result.get('status') == 'failed':
                    failed_task_details.append({
                        'task_id': result.get('task_id', ''),
                        'perception_file_name': Path(result.get('perception_file', '')).name,
                        'rtk_file_name': Path(result.get('rtk_file', '')).name,
                        'error_message': result.get('error_message', '未知错误')
                    })

                # 收集耗时数据
                if result.get('status') == 'completed' and result.get('duration'):
                    durations.append(result.get('duration'))

            data['tasks'] = tasks_data
            
            # 失败任务汇总
            data['has_failed_tasks'] = len(failed_task_details) > 0
            data['failed_task_details'] = failed_task_details
            
            # 性能分析
            if durations:
                data['performance_analysis'] = {
                    'fastest_task': f"{min(durations):.2f}",
                    'slowest_task': f"{max(durations):.2f}",
                    'median_duration': f"{statistics.median(durations):.2f}",
                    'throughput': f"{len(durations) / (sum(durations) / 60):.1f}"
                }
            
            return data
            
        except Exception as e:
            print(f"准备模板数据失败: {e}")
            return {
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'success_rate': '0.0',
                'total_duration': '0.00',
                'average_duration': '0.00',
                'tasks': []
            }
    
    def _find_report_link(self, result: Dict, output_path: str) -> Optional[str]:
        """查找任务的详细报告链接"""
        try:
            task_output_dir = result.get('output_dir')
            if not task_output_dir:
                return None

            # 标准化路径
            task_output_dir = Path(task_output_dir)

            # 查找reports目录下的HTML文件
            reports_dir = task_output_dir / 'reports'
            if not reports_dir.exists():
                return None

            # 查找精度分析报告 - 尝试多种模式
            html_files = []
            patterns = ['*_accuracy_report.html', '*_accuracy_analysis_report.html', '*accuracy*.html']

            for pattern in patterns:
                html_files = list(reports_dir.glob(pattern))
                if html_files:
                    break

            # 如果还是没找到，直接列出所有HTML文件
            if not html_files:
                html_files = list(reports_dir.glob('*.html'))

            if html_files:
                # 计算相对路径
                report_file = html_files[0]
                output_dir = Path(output_path).parent
                try:
                    relative_path = os.path.relpath(report_file, output_dir)
                    return relative_path.replace('\\', '/')  # 确保使用正斜杠
                except ValueError as e:
                    # 如果无法计算相对路径，使用绝对路径
                    return str(report_file).replace('\\', '/')
            # 如果没有找到HTML报告，返回None

            return None

        except Exception as e:
            print(f"查找报告链接失败: {e}")
            return None

    def _find_json_link(self, result: Dict, output_path: str) -> Optional[str]:
        """查找任务的JSON诊断文件链接"""
        try:
            task_output_dir = result.get('output_dir')
            if not task_output_dir:
                return None

            # 查找诊断JSON文件
            task_dir = Path(task_output_dir)
            json_files = list(task_dir.glob('*_diagnostic.json'))
            if json_files:
                # 计算相对路径
                json_file = json_files[0]
                output_dir = Path(output_path).parent
                try:
                    relative_path = os.path.relpath(json_file, output_dir)
                    return relative_path.replace('\\', '/')  # 确保使用正斜杠
                except ValueError:
                    # 如果无法计算相对路径，使用绝对路径
                    return str(json_file).replace('\\', '/')

            return None

        except Exception as e:
            print(f"查找JSON链接失败: {e}")
            return None

    def _extract_task_metrics(self, result: Dict) -> Dict[str, Any]:
        """从任务结果中提取关键指标"""
        metrics = {
            'has_metrics': False,
            'matched_points': 'N/A',
            'coverage_rate': 'N/A',
            'avg_match_score': 'N/A',
            'position_accuracy_mean': 'N/A',
            'speed_accuracy_mean': 'N/A',
            'heading_accuracy_mean': 'N/A',
            'split_count': 'N/A',
            'switch_count': 'N/A',
            'missing_gap_count': 'N/A',
            'total_missing_duration': 'N/A'
        }

        try:
            task_output_dir = result.get('output_dir')
            if not task_output_dir:
                return metrics

            # 查找诊断JSON文件
            task_dir = Path(task_output_dir)
            json_files = list(task_dir.glob('*_diagnostic.json'))
            if not json_files:
                return metrics

            # 读取JSON文件
            with open(json_files[0], 'r', encoding='utf-8') as f:
                diagnostic_data = json.load(f)

            # 提取基础统计信息
            metadata = diagnostic_data.get('metadata', {})
            statistics = diagnostic_data.get('statistics', {})
            accuracy = diagnostic_data.get('accuracy_analysis', {}).get('accuracy_statistics', {})

            # 轨迹匹配指标
            metrics.update({
                'has_metrics': True,
                'matched_points': str(metadata.get('rtk_points_count', 'N/A')),
                'coverage_rate': f"{statistics.get('coverage_rate', 0):.1f}%",
                'avg_match_score': f"{statistics.get('avg_match_score', 0):.3f}",
            })

            # 精度分析指标
            if 'position_accuracy' in accuracy:
                metrics['position_accuracy_mean'] = f"{accuracy['position_accuracy'].get('mean', 0):.3f}m"

            if 'speed_accuracy' in accuracy and 'absolute' in accuracy['speed_accuracy']:
                metrics['speed_accuracy_mean'] = f"{accuracy['speed_accuracy']['absolute'].get('mean', 0):.3f}m/s"

            if 'heading_accuracy' in accuracy:
                metrics['heading_accuracy_mean'] = f"{accuracy['heading_accuracy'].get('mean', 0):.3f}°"

            # 异常检测指标
            metrics.update({
                'split_count': str(statistics.get('split_count', 0)),
                'switch_count': str(statistics.get('switch_count', 0)),
                'missing_gap_count': str(statistics.get('missing_gap_count', 0)),
                'total_missing_duration': f"{statistics.get('total_missing_duration', 0):.1f}s"
            })

            return metrics

        except Exception as e:
            print(f"提取任务指标失败: {e}")
            return metrics
    
    def _format_datetime(self, datetime_str: str) -> str:
        """格式化日期时间字符串"""
        try:
            if not datetime_str:
                return ''
            
            # 解析ISO格式的日期时间
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime('%H:%M:%S')
            
        except Exception:
            return datetime_str
    
    def _render_template(self, template_content: str, data: Dict[str, Any]) -> str:
        """
        简单的模板渲染器，支持Mustache风格的语法
        """
        content = template_content
        
        # 处理简单变量替换 {{variable}}
        for key, value in data.items():
            if not isinstance(value, (list, dict)):
                placeholder = f"{{{{{key}}}}}"
                content = content.replace(placeholder, str(value))
        
        # 处理列表循环 {{#list}} ... {{/list}}
        content = self._render_loops(content, data)
        
        # 处理条件渲染 {{#condition}} ... {{/condition}}
        content = self._render_conditions(content, data)
        
        return content
    
    def _render_loops(self, content: str, data: Dict[str, Any]) -> str:
        """渲染循环结构"""
        import re
        
        # 查找所有循环结构
        loop_pattern = r'\{\{#(\w+)\}\}(.*?)\{\{/\1\}\}'
        
        def replace_loop(match):
            list_name = match.group(1)
            loop_content = match.group(2)
            
            if list_name not in data or not isinstance(data[list_name], list):
                return ''
            
            result = ''
            for item in data[list_name]:
                item_content = loop_content
                
                # 替换循环内的变量
                if isinstance(item, dict):
                    for key, value in item.items():
                        placeholder = f"{{{{{key}}}}}"
                        item_content = item_content.replace(placeholder, str(value))
                
                # 处理嵌套条件
                item_content = self._render_conditions(item_content, item if isinstance(item, dict) else {})
                
                result += item_content
            
            return result
        
        return re.sub(loop_pattern, replace_loop, content, flags=re.DOTALL)
    
    def _render_conditions(self, content: str, data: Dict[str, Any]) -> str:
        """渲染条件结构"""
        import re
        
        # 处理正向条件 {{#condition}} ... {{/condition}}
        condition_pattern = r'\{\{#(\w+)\}\}(.*?)\{\{/\1\}\}'
        
        def replace_condition(match):
            condition_name = match.group(1)
            condition_content = match.group(2)
            
            # 检查条件是否为真
            if condition_name in data and data[condition_name]:
                return condition_content
            return ''
        
        content = re.sub(condition_pattern, replace_condition, content, flags=re.DOTALL)
        
        # 处理反向条件 {{^condition}} ... {{/condition}}
        neg_condition_pattern = r'\{\{\^(\w+)\}\}(.*?)\{\{/\1\}\}'
        
        def replace_neg_condition(match):
            condition_name = match.group(1)
            condition_content = match.group(2)
            
            # 检查条件是否为假
            if condition_name not in data or not data[condition_name]:
                return condition_content
            return ''
        
        content = re.sub(neg_condition_pattern, replace_neg_condition, content, flags=re.DOTALL)
        
        return content

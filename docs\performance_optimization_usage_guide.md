# 性能优化功能使用指南

> **版本**: v1.0  
> **日期**: 2025-01-31  
> **适用于**: 轨迹匹配系统性能优化功能

---

## 📋 概述

本指南介绍如何使用轨迹匹配系统的性能优化功能，包括配置说明、使用方法和故障排除。

### 优化功能特性

- **时间对齐RTK查询**: 使用二分查找优化RTK最近点查询
- **评分缓存系统**: 避免重复评分计算
- **预裁剪机制**: 提前淘汰低质量轨迹段
- **性能监控**: 实时统计优化效果

---

## ⚙️ 配置说明

### 基本配置

在 `config/unified_config.json` 中添加以下配置：

```json
{
  "performance": {
    "enable_time_aligned_search": true,
    "rtk_max_time_diff": 3.0,
    "enable_performance_logging": true
  },
  "scoring": {
    "method": "unified",
    "enable_score_cache": true,
    "pre_filter_threshold": 0.4,
    "cache_debug_output": false
  }
}
```

### 配置参数详解

#### performance 配置项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_time_aligned_search` | bool | true | 是否启用时间对齐RTK查询优化 |
| `rtk_max_time_diff` | float | 3.0 | RTK时间对齐最大允许时间差（秒） |
| `enable_performance_logging` | bool | true | 是否启用性能监控日志 |

#### scoring 配置项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_score_cache` | bool | true | 是否启用评分缓存 |
| `pre_filter_threshold` | float | 0.4 | 预裁剪评分阈值（0-1） |
| `cache_debug_output` | bool | false | 是否输出缓存调试信息 |

### 预设配置文件

#### 性能优先模式
```json
{
  "performance": {
    "enable_time_aligned_search": true,
    "rtk_max_time_diff": 2.0,
    "enable_performance_logging": true
  },
  "scoring": {
    "enable_score_cache": true,
    "pre_filter_threshold": 0.5,
    "cache_debug_output": false
  }
}
```

#### 精度优先模式
```json
{
  "performance": {
    "enable_time_aligned_search": true,
    "rtk_max_time_diff": 4.0,
    "enable_performance_logging": true
  },
  "scoring": {
    "enable_score_cache": true,
    "pre_filter_threshold": 0.2,
    "cache_debug_output": false
  }
}
```

#### 兼容模式（禁用优化）
```json
{
  "performance": {
    "enable_time_aligned_search": false,
    "rtk_max_time_diff": 3.0,
    "enable_performance_logging": false
  },
  "scoring": {
    "enable_score_cache": false,
    "pre_filter_threshold": 0.0,
    "cache_debug_output": false
  }
}
```

---

## 🚀 使用方法

### 基本使用

```python
from core.simple_distance_matcher import SimpleDistanceMatcher
from core.config_loader import load_config

# 加载配置
config = load_config('config/unified_config.json')

# 创建匹配器（自动启用优化功能）
matcher = SimpleDistanceMatcher(config, rtk_points)

# 执行匹配流程
filtered_perception = matcher.filter_perception_points(perception_points)
segments = matcher.build_segments(filtered_perception)
core_chain = matcher.build_core_chain(segments)  # 自动使用优化功能
```

### 性能监控

```python
# 获取性能统计
performance_stats = matcher.performance_monitor.get_stats()
print(f"RTK查询次数: {performance_stats['rtk_query_count']}")
print(f"平均查询时间: {performance_stats['avg_query_time']:.3f}ms")

# 获取缓存统计
cache_stats = matcher.score_cache.get_stats()
print(f"缓存命中率: {cache_stats['hit_rate']:.1%}")
print(f"缓存大小: {cache_stats['cache_size']}")
```

### 调试模式

启用详细调试输出：

```json
{
  "scoring": {
    "cache_debug_output": true
  },
  "performance": {
    "enable_performance_logging": true
  }
}
```

---

## 📊 性能监控

### 日志输出示例

```
=================================================
RTK查询性能统计
=================================================
总查询次数: 1,234
优化查询次数: 1,200 (97.2%)
传统查询次数: 34 (2.8%)
平均查询时间: 0.125ms
总查询时间: 0.154s
性能提升: 8.5×
=================================================

==================================================
评分缓存统计
==================================================
总请求数: 456
缓存命中: 234
缓存未命中: 222
命中率: 51.3%
缓存大小: 89
预估节省时间: 0.234s
==================================================
```

### 性能指标说明

- **RTK查询次数**: 总的RTK最近点查询次数
- **优化查询比例**: 使用优化算法的查询比例
- **平均查询时间**: 单次查询的平均耗时
- **缓存命中率**: 评分缓存的命中率
- **性能提升倍数**: 相对于传统算法的性能提升

---

## 🔧 参数调优

### rtk_max_time_diff 调优

| 值 | 性能 | 精度 | 适用场景 |
|----|------|------|----------|
| 1.0s | 最快 | 较低 | 高频采样，对精度要求不高 |
| 2.0s | 快 | 中等 | 平衡性能和精度 |
| 3.0s | 中等 | 高 | 推荐默认值 |
| 4.0s | 较慢 | 最高 | 对精度要求极高 |
| 5.0s | 慢 | 最高 | 低频采样或噪声数据 |

### pre_filter_threshold 调优

| 值 | 效果 | 风险 | 适用场景 |
|----|------|------|----------|
| 0.0 | 禁用预裁剪 | 无 | 兼容模式 |
| 0.2 | 轻度裁剪 | 低 | 保守模式 |
| 0.4 | 中度裁剪 | 中等 | 推荐默认值 |
| 0.5 | 重度裁剪 | 较高 | 性能优先 |
| 0.6 | 激进裁剪 | 高 | 极端性能优化 |

---

## 🐛 故障排除

### 常见问题

#### 1. 性能提升不明显

**可能原因**:
- RTK数据量较小
- 感知轨迹段数量较少
- 配置参数不当

**解决方案**:
```json
{
  "performance": {
    "enable_time_aligned_search": true,
    "rtk_max_time_diff": 2.0  // 减小时间窗口
  },
  "scoring": {
    "pre_filter_threshold": 0.5  // 提高预裁剪阈值
  }
}
```

#### 2. 精度下降

**可能原因**:
- `rtk_max_time_diff` 设置过小
- `pre_filter_threshold` 设置过高

**解决方案**:
```json
{
  "performance": {
    "rtk_max_time_diff": 4.0  // 增大时间窗口
  },
  "scoring": {
    "pre_filter_threshold": 0.2  // 降低预裁剪阈值
  }
}
```

#### 3. 内存使用过高

**可能原因**:
- RTK数据量过大
- 缓存未及时清理

**解决方案**:
```python
# 手动清理缓存
matcher.score_cache.clear()

# 或禁用缓存
config['scoring']['enable_score_cache'] = False
```

### 调试技巧

1. **启用详细日志**:
   ```json
   {
     "scoring": {"cache_debug_output": true},
     "performance": {"enable_performance_logging": true}
   }
   ```

2. **分步测试**:
   ```python
   # 测试RTK查询优化
   distance = matcher.find_nearest_rtk_distance_optimized(perception_point)
   
   # 测试评分缓存
   score = matcher.score_cache.get_score(segment_id, segment, scorer_func)
   ```

3. **性能对比**:
   ```python
   # 运行精度对比测试
   python tests/accuracy_comparison_test.py
   
   # 运行性能基准测试
   python tests/performance_benchmark_test.py
   ```

---

## 📚 相关文档

- [性能优化计划](performance_optimization_plan.md)
- [配置参数参考](config_reference.md)
- [API文档](api_reference.md)

---

## 📞 技术支持

如有问题或建议，请：
1. 查看本使用指南
2. 运行相关测试脚本
3. 联系开发团队或提交Issue

---

*最后更新: 2025-01-31*

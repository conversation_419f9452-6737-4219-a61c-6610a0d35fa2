# 📚 API参考文档

## 核心模块API

### 1. TrajectoryMatcher 轨迹匹配器

#### 类定义
```python
class TrajectoryMatcher:
    """轨迹匹配核心算法类"""
    
    def __init__(self, config: Dict)
    def process_files(self, perception_file: str, rtk_file: str, output_dir: str) -> Dict
```

#### 主要方法

##### `process_files(perception_file, rtk_file, output_dir)`
处理感知和RTK文件对，执行完整的轨迹匹配流程。

**参数:**
- `perception_file` (str): 感知数据文件路径
- `rtk_file` (str): RTK轨迹文件路径  
- `output_dir` (str): 输出目录路径

**返回:**
- `Dict`: 包含处理结果的字典
  - `status`: 处理状态 ('completed' | 'failed')
  - `output_files`: 生成的输出文件列表
  - `statistics`: 匹配统计信息
  - `error_message`: 错误信息（如果失败）

**示例:**
```python
from core.trajectory_matcher import TrajectoryMatcher

matcher = TrajectoryMatcher(config)
result = matcher.process_files(
    perception_file="data/perception.txt",
    rtk_file="data/rtk.log", 
    output_dir="output/analysis"
)
```

### 2. BatchProcessor 批量处理器

#### 类定义
```python
class BatchProcessor:
    """批量处理核心类"""
    
    def __init__(self, config_path: str)
    def load_batch_tasks(self, batch_file: str) -> List[Dict]
    def process_task(self, task: Dict) -> Dict
    def run_batch(self, batch_file: str, output_dir: str) -> Dict
```

#### 主要方法

##### `load_batch_tasks(batch_file)`
从CSV文件加载批量任务配置。

**参数:**
- `batch_file` (str): 批量任务CSV文件路径

**返回:**
- `List[Dict]`: 任务列表，每个任务包含感知文件和RTK文件路径

##### `process_task(task)`
处理单个批量任务。

**参数:**
- `task` (Dict): 任务配置字典
  - `perception_file`: 感知文件路径
  - `rtk_file`: RTK文件路径
  - `task_id`: 任务ID

**返回:**
- `Dict`: 任务处理结果

##### `run_batch(batch_file, output_dir)`
执行完整的批量处理流程。

**参数:**
- `batch_file` (str): 批量任务文件路径
- `output_dir` (str): 输出目录路径

**返回:**
- `Dict`: 批量处理汇总结果

### 3. BatchReportGenerator 批量报告生成器

#### 类定义
```python
class BatchReportGenerator:
    """批量处理HTML报告生成器"""
    
    def generate_html_report(self, summary_data: Dict, output_path: str) -> None
```

#### 主要方法

##### `generate_html_report(summary_data, output_path)`
生成增强版批量处理HTML报告。

**参数:**
- `summary_data` (Dict): 批量处理汇总数据
  - `total_tasks`: 总任务数
  - `completed_tasks`: 完成任务数
  - `failed_tasks`: 失败任务数
  - `results`: 详细任务结果列表
- `output_path` (str): HTML报告输出路径

**功能特性:**
- 详细任务指标展示（匹配点数、覆盖率、精度分析）
- 异常检测统计（分裂、切换、漏检）
- 智能链接系统（精度报告 + JSON数据）
- 响应式表格设计

**示例:**
```python
from core.batch_report_generator import BatchReportGenerator

generator = BatchReportGenerator()
generator.generate_html_report(
    summary_data=batch_results,
    output_path="output/batch_summary_report.html"
)
```

## 配置系统

### 配置文件结构

```json
{
  "time_sync": {
    "perception_timezone": "Asia/Shanghai",
    "rtk_timezone": "UTC",
    "time_tolerance": 0.1
  },
  "spatial_filter": {
    "roi_enabled": true,
    "roi_bounds": {...},
    "corridor_enabled": true,
    "corridor_width": 10.0
  },
  "matching": {
    "distance_threshold": 5.0,
    "time_window": 1.0,
    "min_match_score": 0.5
  },
  "anomaly_detection": {
    "split_detection": true,
    "id_switch_detection": true,
    "missing_gap_detection": true
  },
  "accuracy_analysis": {
    "position_analysis": true,
    "speed_analysis": true,
    "heading_analysis": true
  }
}
```

### 配置加载

```python
from core.config_loader import ConfigLoader

config = ConfigLoader.load_config("config/unified_config.json")
```

## 数据格式

### 感知数据格式 (JSON)
```json
{
  "timestamp": "2023-01-01T12:00:00.000Z",
  "objects": [
    {
      "id": "obj_001",
      "position": {"x": 100.0, "y": 200.0},
      "velocity": {"vx": 10.0, "vy": 0.0},
      "heading": 90.0
    }
  ]
}
```

### RTK数据格式 (LOG)
```
timestamp,latitude,longitude,altitude,heading,speed
2023-01-01T12:00:00.000Z,39.9042,116.4074,50.0,90.0,36.0
```

## 输出格式

### 匹配结果 (CSV)
```csv
timestamp,perception_id,rtk_timestamp,distance,match_score,position_error
2023-01-01T12:00:00.000Z,obj_001,2023-01-01T12:00:00.100Z,2.5,0.85,2.1
```

### 诊断信息 (JSON)
```json
{
  "metadata": {
    "perception_file": "data/perception.txt",
    "rtk_file": "data/rtk.log",
    "processing_time": 1.23
  },
  "statistics": {
    "total_matches": 1500,
    "coverage_rate": 0.95,
    "avg_match_score": 0.82
  },
  "accuracy_analysis": {
    "position_accuracy": {"mean": 1.2, "std": 0.8},
    "speed_accuracy": {"mean": 0.5, "std": 0.3},
    "heading_accuracy": {"mean": 2.1, "std": 1.5}
  },
  "anomaly_detection": {
    "split_count": 2,
    "switch_count": 1,
    "missing_gaps": [...]
  }
}
```

## 错误处理

### 常见错误类型

1. **文件格式错误**
   - 错误码: `FORMAT_ERROR`
   - 描述: 输入文件格式不支持或损坏

2. **时间同步失败**
   - 错误码: `TIME_SYNC_ERROR`
   - 描述: 时间戳格式错误或时区转换失败

3. **匹配失败**
   - 错误码: `MATCHING_ERROR`
   - 描述: 轨迹匹配算法执行失败

4. **输出错误**
   - 错误码: `OUTPUT_ERROR`
   - 描述: 结果文件写入失败

### 错误处理示例

```python
try:
    result = matcher.process_files(perception_file, rtk_file, output_dir)
    if result['status'] == 'failed':
        print(f"处理失败: {result['error_message']}")
except Exception as e:
    print(f"系统错误: {e}")
```

## 性能优化

### 并行处理配置

```python
# 自动检测最优进程数
optimal_workers = min(cpu_count(), task_count, 8)

# 手动指定进程数
python batch_parallel_linux_optimized.py --workers 4
```

### 内存优化建议

1. **大文件处理**: 使用流式读取避免内存溢出
2. **批量大小**: 建议单批次不超过100个任务
3. **并行进程**: 根据内存大小调整进程数

## 扩展开发

### 自定义匹配算法

```python
class CustomMatcher(TrajectoryMatcher):
    def custom_matching_algorithm(self, perception_data, rtk_data):
        # 实现自定义匹配逻辑
        pass
```

### 自定义报告生成

```python
class CustomReportGenerator(BatchReportGenerator):
    def generate_custom_report(self, data, template_path):
        # 实现自定义报告生成
        pass
```

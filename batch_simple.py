#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车路协同轨迹匹配系统 - 串行批量处理工具

功能描述:
    简化版批量轨迹匹配处理工具，专注于核心功能，稳定可靠。
    适用于小批量数据处理、调试测试和内存受限环境。
    支持增强HTML报告生成和详细的错误追踪。

特性:
    - 串行处理模式，稳定可靠
    - 详细的进度监控和错误报告
    - 增强版HTML报告生成
    - 支持任务级错误恢复
    - 适合调试和小批量处理

使用方法:
    python batch_simple.py --batch data/batch.csv --config config/unified_config.json
    python batch_simple.py --batch data/batch.csv --output output/results
    python batch_simple.py --help

作者: 车路协同轨迹匹配系统开发团队
版本: 2.0
创建时间: 2025-07-31
最后更新: 2025-07-31
"""

import sys
import os
import csv
import argparse
import json
from pathlib import Path
from datetime import datetime
import logging
from typing import List, Dict

# 添加core模块到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# 导入现有的轨迹匹配器
import core.trajectory_matcher as tm
from batch_report_generator import BatchReportGenerator


class BatchProcessor:
    """批量处理器 - 简化版"""
    
    def __init__(self, config_path: str = "config/unified_config.json"):
        self.config_path = config_path
        self.results: List[Dict] = []
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('batch_processing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def parse_batch_file(self, batch_file: str) -> List[Dict]:
        """解析batch.csv文件"""
        tasks = []
        
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for i, row in enumerate(reader):
                    if 'test_file' not in row or 'rtk_file' not in row:
                        self.logger.warning(f"第{i+1}行缺少必要字段，跳过")
                        continue
                    
                    perception_file = row['test_file'].strip()
                    rtk_file = row['rtk_file'].strip()
                    
                    # 验证文件存在
                    if not Path(perception_file).exists():
                        self.logger.warning(f"感知文件不存在: {perception_file}")
                        continue
                    
                    if not Path(rtk_file).exists():
                        self.logger.warning(f"RTK文件不存在: {rtk_file}")
                        continue
                    
                    task = {
                        'task_id': f"task_{i+1:03d}",
                        'perception_file': perception_file,
                        'rtk_file': rtk_file
                    }
                    tasks.append(task)
                    
                    self.logger.info(f"添加任务 {task['task_id']}: {perception_file} -> {rtk_file}")
        
        except Exception as e:
            self.logger.error(f"解析batch文件失败: {e}")
            raise
        
        return tasks
    
    def execute_task(self, task: Dict, output_base_dir: str) -> Dict:
        """执行单个任务"""
        start_time = datetime.now()
        
        # 创建任务专用输出目录
        perception_name = Path(task['perception_file']).stem
        rtk_name = Path(task['rtk_file']).stem
        task_output_dir = Path(output_base_dir) / f"{perception_name}_vs_{rtk_name}"
        task_output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            self.logger.info(f"开始执行任务 {task['task_id']}")
            
            # 备份原始sys.argv
            original_argv = sys.argv
            
            # 构建trajectory_matcher参数
            tm_args = [
                'trajectory_matcher.py',
                '--rtk', task['rtk_file'],
                '--perception', task['perception_file'],
                '--config', self.config_path,
                '--output-dir', str(task_output_dir)
            ]
            
            # 设置新的argv并调用trajectory_matcher
            sys.argv = tm_args
            tm.main()
            
            # 恢复原始argv
            sys.argv = original_argv
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                'task_id': task['task_id'],
                'perception_file': task['perception_file'],
                'rtk_file': task['rtk_file'],
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'output_dir': str(task_output_dir)
            }
            
            self.logger.info(f"任务 {task['task_id']} 完成，耗时 {duration:.2f}秒")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                'task_id': task['task_id'],
                'perception_file': task['perception_file'],
                'rtk_file': task['rtk_file'],
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error_message': str(e),
                'output_dir': str(task_output_dir)
            }
            
            self.logger.error(f"任务 {task['task_id']} 失败: {e}")
            return result
    
    def process_batch(self, batch_file: str, output_dir: str = "output/batch_results") -> Dict:
        """处理批量任务"""
        self.logger.info("开始批量处理")
        
        # 解析任务
        tasks = self.parse_batch_file(batch_file)
        if not tasks:
            raise ValueError("没有有效的任务需要处理")
        
        self.logger.info(f"共解析到 {len(tasks)} 个任务")
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 执行任务
        self.results = []
        for i, task in enumerate(tasks, 1):
            self.logger.info(f"处理进度: {i}/{len(tasks)}")
            result = self.execute_task(task, output_dir)
            self.results.append(result)
        
        # 生成汇总报告
        summary = self.generate_summary(output_dir)
        
        self.logger.info("批量处理完成")
        return summary
    
    def generate_summary(self, output_dir: str) -> Dict:
        """生成汇总报告"""
        completed_tasks = [r for r in self.results if r['status'] == 'completed']
        failed_tasks = [r for r in self.results if r['status'] == 'failed']

        summary = {
            'total_tasks': len(self.results),
            'completed_tasks': len(completed_tasks),
            'failed_tasks': len(failed_tasks),
            'success_rate': len(completed_tasks) / len(self.results) * 100 if self.results else 0,
            'total_duration': sum(r.get('duration', 0) for r in self.results),
            'average_duration': sum(r.get('duration', 0) for r in completed_tasks) / len(completed_tasks) if completed_tasks else 0,
            'results': self.results
        }

        # 保存JSON报告
        summary_file = Path(output_dir) / "batch_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # 保存CSV报告
        csv_file = Path(output_dir) / "batch_summary.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if self.results:
                fieldnames = ['task_id', 'perception_file', 'rtk_file', 'status',
                            'start_time', 'end_time', 'duration', 'error_message', 'output_dir']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for result in self.results:
                    row = {k: result.get(k, '') for k in fieldnames}
                    writer.writerow(row)

        # 生成HTML报告
        try:
            html_file = Path(output_dir) / "batch_summary_report.html"
            report_generator = BatchReportGenerator()
            report_generator.generate_html_report(summary, str(html_file))
            self.logger.info(f"HTML报告已保存: {html_file}")
        except Exception as e:
            self.logger.warning(f"生成HTML报告失败: {e}")

        self.logger.info(f"汇总报告已保存: {summary_file}")
        return summary


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="简化版批量轨迹匹配处理工具")
    parser.add_argument('--batch', required=True, help='批量任务文件路径 (batch.csv)')
    parser.add_argument('--config', default='config/unified_config.json', help='配置文件路径')
    parser.add_argument('--output', default='output/batch_results', help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.batch).exists():
        print(f"错误：批量任务文件不存在: {args.batch}")
        sys.exit(1)
    
    if not Path(args.config).exists():
        print(f"错误：配置文件不存在: {args.config}")
        sys.exit(1)
    
    try:
        # 创建批量处理器
        processor = BatchProcessor(args.config)
        
        # 执行批量处理
        summary = processor.process_batch(args.batch, args.output)
        
        # 输出结果
        print(f"\n[SUCCESS] 批量处理完成！")
        print(f"总任务数: {summary['total_tasks']}")
        print(f"成功任务: {summary['completed_tasks']}")
        print(f"失败任务: {summary['failed_tasks']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总耗时: {summary['total_duration']:.2f}秒")
        print(f"平均耗时: {summary['average_duration']:.2f}秒")
        print(f"输出目录: {args.output}")

        # 如果有失败任务，显示详细信息
        if summary['failed_tasks'] > 0:
            print(f"\n[ERROR] 失败任务详情:")
            for result in summary['results']:
                if result['status'] == 'failed':
                    print(f"  - {result['task_id']}: {result.get('error_message', '未知错误')}")

    except Exception as e:
        print(f"[ERROR] 批量处理失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
